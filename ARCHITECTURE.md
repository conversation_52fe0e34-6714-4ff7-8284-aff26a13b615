# アーキテクチャドキュメント v2.0

## 概要

CITER and other AI Agents v2.0は、機能別に整理されたモジュール構造を採用し、保守性と拡張性を大幅に向上させました。

## 設計原則

### 1. 関心の分離 (Separation of Concerns)
- 各機能が独立したモジュールに分離
- 共通機能は`shared`モジュールに集約
- APIレイヤーとビジネスロジックの分離

### 2. 依存関係の管理
- 循環依存の回避
- 明確な依存関係の定義
- インターフェースベースの設計

### 3. 設定の統一管理
- 環境変数の一元管理
- 機能別設定クラス
- 型安全な設定

## モジュール構成

### app/config/
**責任**: アプリケーション全体の設定管理

- `settings.py`: Pydantic Settingsを使用した型安全な設定管理
- `database.py`: SQLAlchemy設定とセッション管理

**特徴**:
- 環境変数の自動読み込み
- 機能別設定クラス
- 開発/本番環境の自動切り替え

### app/shared/
**責任**: 全機能で共有される基盤機能

#### shared/auth/
- `models.py`: ユーザー、サブスクリプション等の認証関連モデル
- `backends.py`: JWT認証、OAuth設定
- `manager.py`: FastAPI-Usersユーザー管理
- `dependencies.py`: 認証依存関係、ロール制限
- `schemas.py`: 認証関連のPydanticスキーマ

#### shared/database/
- `base.py`: 共通ベースモデル
- `session.py`: データベースセッション管理

#### shared/utils/
- `logging.py`: ログ設定
- `exceptions.py`: カスタム例外クラス

### app/features/
**責任**: 各機能の実装

#### features/citer2/
文献検索機能の実装

- `models.py`: 検索ジョブ、引用情報等のモデル
- `services.py`: 文献検索のビジネスロジック
- `pipeline.py`: 文献検索パイプライン
- `schemas.py`: API入出力スキーマ

**主要機能**:
- PubMed検索
- 論文スコアリング
- 引用生成
- RIS出力

#### features/writer/
論文作成機能の実装

- `models.py`: OCRジョブ、論文データ等のモデル
- `ocr.py`: Azure Computer Vision統合
- `services.py`: 論文生成ビジネスロジック
- `schemas.py`: API入出力スキーマ

**主要機能**:
- OCR処理
- 論文自動生成
- セクション識別
- 文献自動追加

#### features/todos/
Gmail ToDo機能の実装（今後実装予定）

#### features/billing/
課金機能の実装（今後実装予定）

### app/api/
**責任**: APIレイヤーの実装

- `dependencies.py`: API共通依存関係
- `routes/`: 機能別APIルート

**特徴**:
- RESTful API設計
- OpenAPI/Swagger自動生成
- 統一されたエラーハンドリング

## データフロー

### 1. リクエスト処理フロー

```
Client Request
    ↓
FastAPI Router (app/api/routes/)
    ↓
Authentication/Authorization (app/shared/auth/)
    ↓
Business Logic (app/features/*/services.py)
    ↓
Data Access (app/features/*/models.py)
    ↓
Database (SQLAlchemy)
```

### 2. 設定読み込みフロー

```
Environment Variables
    ↓
Pydantic Settings (app/config/settings.py)
    ↓
Feature-specific Settings
    ↓
Application Components
```

## データベース設計

### 共通テーブル
- `user`: ユーザー情報（FastAPI-Users準拠）
- `subscription`: サブスクリプション情報
- `usage_records`: 使用記録

### 機能別テーブル
- CITER2: `citer2_search_jobs`, `citer2_citations`, etc.
- Writer: `writer_ocr_jobs`, `writer_papers`, etc.

## セキュリティ

### 認証・認可
- JWT認証（24時間有効期限）
- Google OAuth2統合
- ロールベースアクセス制御

### データ保護
- 環境変数による機密情報管理
- SQLインジェクション対策（SQLAlchemy ORM）
- CORS設定

## パフォーマンス

### 非同期処理
- FastAPI非同期サポート
- SQLAlchemy非同期セッション
- バックグラウンドタスク

### キャッシュ戦略
- 設定値のメモリキャッシュ
- データベースクエリ最適化

## 監視・ログ

### ログ設定
- 構造化ログ
- レベル別ログ出力
- 機能別ログ分離

### メトリクス
- API使用量追跡
- エラー率監視
- パフォーマンス指標

## 拡張性

### 新機能追加
1. `app/features/new_feature/`ディレクトリ作成
2. モデル、サービス、スキーマ実装
3. APIルート追加
4. テスト作成

### 設定追加
1. `app/config/settings.py`に設定クラス追加
2. 環境変数定義
3. 機能での設定利用

## テスト戦略

### ユニットテスト
- 各モジュールの独立テスト
- モック使用による依存関係分離

### 統合テスト
- API エンドポイントテスト
- データベース統合テスト

### テスト環境
- インメモリSQLite使用
- テスト専用設定

## デプロイメント

### 開発環境
- SQLite使用
- ホットリロード有効
- デバッグログ出力

### 本番環境
- PostgreSQL使用
- 環境変数による設定
- 構造化ログ出力

## 今後の改善点

1. **キャッシュ層の追加**: Redis統合
2. **メッセージキュー**: Celery/RQ統合
3. **マイクロサービス化**: 機能別サービス分離
4. **API バージョニング**: v1/v2並行運用
5. **監視強化**: Prometheus/Grafana統合
