# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
venv_new/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README*.md
ARCHITECTURE.md
MIGRATION.md
*.txt
*.docx

# Logs
*.log
logs/

# Database
*.sqlite3
*.db

# Temporary files
tmp/
temp/
.tmp/

# Test files
tests/
pytest.ini
.coverage
htmlcov/

# Development files
.env.local
.env.development
.env.test

# Node modules (if any)
node_modules/

# Backup files
*.bak
*.backup

# Old structure (v1.0)
agent/

# Build artifacts
*.tar.gz
*.zip