<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Literature Search Agent ver.2.1</title>
  <link rel="stylesheet" href="/static/css/base.css" />
  <link rel="stylesheet" href="/static/css/lsa.css" />
</head>
<body>
  <header class="topbar">
    <h1 class="logo">
      <a href="/" class="logo-link">MyAI<span>Agents</span></a>
    </h1>
    <div id="userBox" class="badge" onclick="toggleMenu()">
      <span id="userName">guest</span>
      <span id="slash"> / </span>
      <span id="userRole">guest</span>
      <div id="menu" hidden>
        <a href="#" onclick="buy();return false;">Buy Credits</a>
        <a href="/profile.html">Profile</a>
        <a href="#" onclick="logout();return false;">Logout</a>
      </div>
    </div>
    <span id="cost" style="margin-left:16px"></span>
  </header>

  <main class="page-main">

    <section class="plan-compare">
      <table class="plan-compare-table">
        <tr>
          <th>Feature</th>
          <th>Free</th>
          <th>Premium</th>
        </tr>
        <tr><td>Quick search (≤10 results)</td><td>✔️</td><td>✔️</td></tr>
        <tr><td>AI evidence score/explanation</td><td>✖️</td><td>✔️ <span class="premium-badge">PREMIUM</span></td></tr>
        <tr><td>EndNote/RIS export</td><td>✔️</td><td>✔️ <span class="premium-badge">PREMIUM</span></td></tr>
        <tr><td>AI statement rewrite</td><td>✖️</td><td>✔️ <span class="premium-badge">(TO BE ADDED)</span></td></tr>
        <tr><td>Rejected reference analysis</td><td>✖️</td><td>✔️ <span class="premium-badge">(TO BE ADDED)</span></td></tr>
      </table>
      <button class="buy-premium-btn" onclick="buy()">Upgrade to Premium</button>
    </section>

    <section class="about lang-en info-box">
      <h2>Literature Search Agent (CITER&nbsp;ver.2.1)</h2>
      <p>
        Enter your statement or paragraph in natural language. The AI will search PubMed, score each paper's relevance, and produce citation tags and RIS files ready for EndNote.
      </p>
    </section>
  </main>

  <div style="margin-bottom:10px">
    Search Power (%)
    <select id="hits"><option>10</option><option>50</option><option>100</option></select>
    Score Threshold
    <select id="thr"><option value="4" selected>&ge;4</option><option value="5">&ge;5</option></select>
    MaxRef per sentence
    <select id="maxr"><option>3</option><option>5</option><option>10</option></select>
  </div>

  <textarea id="q" placeholder="Please input your paragraph."></textarea><br />
  <button id="ask" onclick="send()">Ask</button>
  <button type="button" onclick="setExample()">Set a sample sentence</button>

  <progress id="bar" value="0" max="100" hidden></progress>
  <div id="step" style="margin-bottom:14px;"></div>

  <h2>Cited Text
    <button id="cpy" class="copyBtn" hidden onclick="copyCited()">Copy</button>
  </h2>
  <pre id="cited"></pre>

  <h2>Reference list (APA)</h2>
  <div id="refListAPA"></div>

  <h2>RIS / Report</h2>
  <div id="ris"></div>
  <pre id="detail" style="margin-top:12px;"></pre>

  <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  <script type="module" src="/static/js/auth.js"></script>
  <script>
    // 関数を先に定義してグローバルに公開
    function setExample() {
      const q = document.getElementById('q');
      if (q) q.value = "The silkworm (Bombyx mori) based infection models have accomplished the discovery of novel antibiotics.";
    }

    function copyCited() {
      const cited = document.getElementById('cited');
      if (cited) {
        navigator.clipboard.writeText(cited.textContent)
          .then(() => toast('Copied', 'success'))
          .catch(() => toast('Copy failed', 'error'));
      }
    }

    function toast(msg, type) {
      Toastify({
        text: msg,
        duration: 3000,
        gravity: 'top',
        position: 'right',
        style: { background: type === 'error' ? '#d9534f' : '#198754' }
      }).showToast();
    }

    // buy関数を定義（auth.jsのbuy関数と同じ機能）
    function buy() {
      // auth.jsのbuy関数が利用可能な場合はそれを使用、そうでなければ直接遷移
      if (window.buy && window.buy !== buy) {
        window.buy();
      } else {
        // Stripe Checkoutに遷移
        window.location.href = "https://buy.stripe.com/test_28o5lE8Qs8Ry5Gg9AA";
      }
    }

    // グローバルに公開
    window.setExample = setExample;
    window.copyCited = copyCited;
    window.buy = buy;

    let evtSource = null;
    let currentJobId = null;
        
    async function startJob() {
      const txt = document.getElementById("q").value.trim();
      if (!txt) { alert("Please input some text"); return; }

      // 1. ジョブ開始API呼び出し
      const token = localStorage.getItem("jwt");
      const res = await fetch(`/api/run_stream_start?text=${encodeURIComponent(txt)}`, {
        headers: { Authorization: "Bearer " + token }
      });
      if (!res.ok) {
        alert("Failed to start job"); return;
      }
      const data = await res.json();
      currentJobId = data.job_id;

      // 2. SSE接続で進捗取得開始
      if (evtSource) evtSource.close();
      evtSource = new EventSource(`/api/run_stream_events?job_id=${currentJobId}`);

      evtSource.addEventListener('init', e => { console.log("Init", e.data); /* 初期化 */ });
      evtSource.addEventListener('progress', e => {
        const progress = JSON.parse(e.data);
        console.log("Progress", progress);
        // 進捗バーやログ更新
      });
      evtSource.addEventListener('done', e => {
        const result = JSON.parse(e.data);
        console.log("Done", result);
        // UI反映（引用文、RISリンクなど）
        evtSource.close();
      });
      evtSource.onerror = e => {
        console.error("EventSource error", e);
        evtSource.close();
      };
    }


    function $(id){ return document.getElementById(id); }
    const askBtn   = $('ask'),
          bar      = $('bar'),
          step     = $('step'),
          cited    = $('cited'),
          ris      = $('ris'),
          detail   = $('detail'),
          cpy      = $('cpy'),
          hitsSel  = $('hits'),
          thrSel   = $('thr'),
          maxSel   = $('maxr'),
          costBadge= $('cost'),
          refListAPA = $('refListAPA');

    const SAMPLE_TEXT = "The silkworm (Bombyx mori) based infection models have accomplished the discovery of novel antibiotics.";

    window.onload = function() {
      const q = $('q');
      if (q && !q.value.trim()) q.value = SAMPLE_TEXT;

      // auth.jsの関数が読み込まれるのを待つ
      const checkAuthFunctions = () => {
        if (typeof window.buy === 'function' && window.buy !== buy) {
          // auth.jsのbuy関数が利用可能になった場合、それを使用
          console.log('Auth functions loaded');
        } else {
          // まだ読み込まれていない場合は少し待つ
          setTimeout(checkAuthFunctions, 100);
        }
      };
      setTimeout(checkAuthFunctions, 100);
    };

    // setExample関数は上部で既に定義済み

    window.send = async function() {
      askBtn.disabled = true;
      askBtn.textContent = 'Running…';
      cited.textContent = ris.textContent = detail.textContent = '';
      refListAPA.innerHTML = '';
      cpy.hidden = true;
      bar.hidden = false; bar.value = 20;
      step.textContent = 'Running...'; costBadge.textContent = '';

      const txt = $('q').value.trim();
      if (!txt) { toast('Please input your text.', 'error'); resetBtn(); return; }
      const token = localStorage.getItem('jwt');
      if (!token) {
        toast('Please log in.', 'error');
        resetBtn();
        return;
      }

      const params = new URLSearchParams({
        text: txt,
        hits: hitsSel.value,
        threshold: thrSel.value,
        max_refs: maxSel.value
      });

      try {
        const res = await fetch('/api/run_stream_get?' + params.toString(), {
          headers: { 'Authorization': 'Bearer ' + token }
        });
        if (!res.ok) {
          if (res.status === 401) {
            toast('Unauthorized or session expired. Please log in again.', 'error');
          } else {
            toast('Server error.', 'error');
          }
          resetBtn();
          return;
        }
        const data = await res.json();

        // Cited text update
        cited.textContent = data.cited || '';
        cpy.hidden = !data.cited;

        // RIS download link
        ris.innerHTML = '';
        if (data.ris_url) {
          const dl = document.createElement('a');
          dl.href = data.ris_url;
          dl.textContent = 'Download RIS';
          dl.className = 'dlBtn';
          dl.download = 'references.ris';
          ris.appendChild(dl);
        }

        // References list rendering (limit by user role)
        const isPremium = window.currentUser?.role === 2;
        console.log('DEBUG: Full response data:', data);
        console.log('DEBUG: Received data.references:', data.references);
        console.log('DEBUG: References type:', typeof data.references);
        console.log('DEBUG: References length:', (data.references || []).length);
        if (data.references && data.references.length > 0) {
          console.log('DEBUG: First reference:', data.references[0]);
          console.log('DEBUG: First reference keys:', Object.keys(data.references[0]));
        } else {
          console.log('DEBUG: No references found or empty array');
        }

        const renderedHTML = renderReferencesAPA(data.references || [], isPremium);
        console.log('DEBUG: Rendered HTML:', renderedHTML);
        refListAPA.innerHTML = renderedHTML;

        // Detail Report (toggle button)
        detail.textContent = '';
        if (data.detail_json) {
          const btn = document.createElement('button');
          btn.textContent = 'Show Detail';
          btn.className = 'dlBtn';
          btn.style.marginLeft = '8px';
          let loaded = false, visible = false;
          btn.onclick = async () => {
            if (!loaded) {
              const js = await fetch(data.detail_json).then(r => r.json());
              detail.textContent = JSON.stringify(js, null, 2);
              loaded = true;
            }
            visible = !visible;
            detail.style.display = visible ? 'block' : 'none';
            btn.textContent = visible ? 'Hide Detail' : 'Show Detail';
          };
          ris.appendChild(btn);
        }

        askBtn.textContent = 'Done!';
        bar.value = 100;
        setTimeout(resetBtn, 1800);
        step.textContent = 'Done!';

      } catch (err) {
        toast('Communication error or server down.', 'error');
        resetBtn();
      }
    };



    // toast関数とcopyCited関数は上部で既に定義済み

    function resetBtn() {
      askBtn.textContent = 'Ask';
      askBtn.disabled = false;
      bar.hidden = true;
    }
    

    // Reference 関連 関数群
    function formatAuthorsAPA(authors) {
      console.log('DEBUG: formatAuthorsAPA called with:', authors);

      if (!authors || authors.length === 0) {
        console.log('DEBUG: No authors, returning empty string');
        return "";
      }

      if (authors.length === 1) {
        console.log('DEBUG: Single author:', authors[0]);
        return authors[0];
      }

      if (authors.length <= 5) {
        const result = authors.slice(0, -1).join(", ") + ", & " + authors[authors.length -1];
        console.log('DEBUG: Multiple authors (<=5):', result);
        return result;
      }

      // 6人以上は3人目以降「et al.」
      const result = authors.slice(0,3).join(", ") + ", et al.";
      console.log('DEBUG: Many authors (>5):', result);
      return result;
    }

    function formatReferenceAPA(ref) {
      console.log('DEBUG: formatReferenceAPA called with:', ref);

      const raw = ref.authors || ref.author || "";
      console.log('DEBUG: raw authors:', raw);

      let authorArr;
      if (Array.isArray(ref.authors)) {
        // authorsが配列の場合
        authorArr = ref.authors.filter(a => a && a.trim().length > 0);
      } else {
        // authorsが文字列の場合
        authorArr = raw
          .split(";")
          .map(a => a.trim())
          .filter(a => a.length > 0);
      }

      console.log('DEBUG: authorArr:', authorArr);

      const authors = formatAuthorsAPA(authorArr);
      const year = ref.year ? `(${ref.year}).` : "";
      const title = ref.title || "";
      const journal = ref.journal || "";
      const vol = ref.volume ? `, ${ref.volume}` : "";
      const issue = ref.issue ? `(${ref.issue})` : "";
      const pages = ref.pages ? `, ${ref.pages}` : "";

      const result = `${authors} ${year} ${title}. <i>${journal}</i>${vol}${issue}${pages}.`;
      console.log('DEBUG: formatted result:', result);

      return result;
    }

    function renderReferencesAPA(refs, isPremium) {
      console.log('DEBUG: renderReferencesAPA called with:', refs, 'isPremium:', isPremium);

      if (!refs || !Array.isArray(refs)) {
        console.log('DEBUG: refs is not a valid array:', refs);
        return '<p>No references available</p>';
      }

      const max = isPremium ? refs.length : Math.min(5, refs.length);
      console.log('DEBUG: max references to show:', max);

      let html = "<ol>";
      for(let i=0; i < max; i++) {
        const item = refs[i];
        console.log(`DEBUG: Processing reference ${i}:`, item);

        try {
          const formattedRef = formatReferenceAPA(item);
          console.log(`DEBUG: Formatted reference ${i}:`, formattedRef);
          html += `<li><a href="https://pubmed.ncbi.nlm.nih.gov/${item.pmid}" target="_blank" rel="noopener">${formattedRef}</a></li>`;
        } catch (error) {
          console.error(`DEBUG: Error formatting reference ${i}:`, error);
          html += `<li>Error formatting reference ${i}</li>`;
        }
      }
      html += "</ol>";

      if(!isPremium && refs.length > max) {
        html += '<p style="color:#fb6136; font-weight:bold;">Unlock all references with Premium!</p>';
      }

      console.log('DEBUG: Final HTML:', html);
      return html;  // innerHTMLセットは呼び出し元で
    }

    function renderReferences(refs, isPremium) {
      const max = isPremium ? refs.length : Math.min(5, refs.length);
      let html = '<ol>';
      for (let i = 0; i < max; i++) {
        const r = refs[i];
        html += `<li>
          <a href="https://pubmed.ncbi.nlm.nih.gov/${r.pmid}" target="_blank" rel="noopener">${r.title}</a>
          ${r.accepted ? '<span style="color:green;font-weight:bold;"> [Accepted]</span>' : '<span style="color:#fb6136;"> [Rejected]</span>'}
        </li>`;
      }
      html += '</ol>';
      if (!isPremium && refs.length > max) {
        html += `<div style="text-align:center; color:#fb6136; font-weight:bold; margin-top:8px;">
                  Unlock full reference list with Premium!
                </div>`;
      }
      $('refList').innerHTML = html;
    }
    
  </script>
</body>
</html>