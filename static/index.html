<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <title>MyAI Apps – Dashboard</title>
  <link rel="stylesheet" href="/static/css/base.css">
  <script>
    (function () {
      const p = new URLSearchParams(location.search);
      const token = p.get('jwt');
      if (token) {
        localStorage.setItem('jwt', token);           // 保存
        history.replaceState({}, '', location.pathname + location.hash); // URL をきれいに
      }
    })();
  </script>
</head>
<body>

  <header class="topbar">
    <h1 class="logo">
      <a href="/" class="logo-link">MyAI<span>Agents</span></a>
    </h1>

    <!-- ★ userBox の中身は auth.js が書き換えるので構造はそのまま -->
    <div id="userBox" class="badge" onclick="toggleMenu()">
      <span id="userName">guest</span>
      <span id="slash"> / </span>
      <span id="userRole">guest</span>
      <div id="menu" hidden>
        <a href="#" onclick="buy();return false;">Buy Credits</a>
        <a href="/profile.html">Profile</a>
        <a href="#" onclick="logout();return false;">Logout</a>
      </div>
    </div>
  </header>

  <main class="grid">
    <!-- 既存のカード -->
    <div class="card" onclick="location='/lsa.html'">
      <h2>Literature Search Agent<br>"CITER v2"</h2>
      <p>Find supporting papers automatically.</p>
    </div>
    <!-- 追加した Gmail ToDo Dashboard のカード -->
    <div class="card" onclick="location='/todos.html'">
      <h2>Gmail Agent</h2>
      <p>Automatically extract and manage your tasks from Gmail.</p>
    </div>
    <!-- 追加した Writer のカード -->
    <div class="card" onclick="location='/writer.html'">
      <h2>The Writer</h2>
      <p>Autowrite from your lab book.</p>
    </div>
    <div class="card disabled">
      <h2>KAKENHI Agent</h2>
      <p>Coming soon</p>
    </div>
    <div class="card disabled">
      <h2>The General Agent</h2>
      <p>Coming soon</p>
    </div>
    <div class="card disabled">
      <h2>RNAseq Agent</h2>
      <p>Coming soon</p>
    </div>
  
  


  </main>

  <!-- ★ Toastify は auth.js で使うため読み込み -->
  <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  <script type="module" src="/static/js/auth.js"></script>

  <footer class="site-footer">
    <a href="/commercial_disclosure.html">Commercial Disclosure</a>
    <a href="/terms_of_service.html">Terms of Service</a>
  </footer>
</body>
</html>