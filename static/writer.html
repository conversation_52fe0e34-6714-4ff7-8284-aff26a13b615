<!doctype html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <title>The Writer ver.1.5</title>
  <link rel="stylesheet" href="/static/css/base.css">
  <link rel="stylesheet" href="https://unpkg.com/mvp.css">
  <link rel="stylesheet" href="/static/css/writer.css">
  <link rel="stylesheet" href="/static/css/paywall.css">
  
  <style>
    textarea { width: 100%; height: 14rem; }
    .hidden { display:none; }
    .paper-area { height: 20rem; }

    /* 表構造表示の追加スタイル */
    .table-display-container {
      margin: 15px 0;
    }

    .table-toggle-btn {
      background-color: #28a745;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
      margin-bottom: 10px;
    }

    .table-toggle-btn:hover {
      background-color: #218838;
    }
  </style>
  <!-- PDF.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
  <script>
    (()=>{const p=new URLSearchParams(location.search);const j=p.get('jwt');
      if(j){localStorage.setItem('jwt',j);
        history.replaceState({},'',location.pathname+location.hash);}})();
  </script>
</head>
<body>

<header class="topbar">
    <h1 class="logo">
      <a href="/" class="logo-link">MyAI<span>Agents</span></a>
    </h1>

    <!-- ★ userBox の中身は auth.js が書き換えるので構造はそのまま -->
    <div id="userBox" class="badge" onclick="toggleMenu()">
      <span id="userName">guest</span>
      <span id="slash"> / </span>
      <span id="userRole">guest</span>
      <div id="menu" hidden>
        <a href="#" onclick="buy();return false;">Buy Credits</a>
        <a href="/profile.html">Profile</a>
        <a href="#" onclick="logout();return false;">Logout</a>
      </div>
    </div>
  </header>


 <div class="content">
  <h1>The Writer</h1>
  <div style="margin: 10px 0; padding: 10px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 4px;">
    <strong>開発者向け:</strong>
    <a href="#" onclick="openTestPage(); return false;" style="color: #0066cc; text-decoration: underline;">文献追加テストページ</a>
    - 論文テキストを直接入力して文献追加機能をテストできます
  </div>
 </div>

  <!-- ① 画像アップロード -->
  <section>
    <h2>1. Upload your handwritten lab book (PDF or image files)</h2>
    <input type="file" id="imgFiles" accept=".png,.jpg,.jpeg,.gif,.webp,.pdf" multiple>
    <button id="btnUpload">UPLOAD & OCR</button>
    <p id="noteStatus"></p>
  </section>


  <!-- ② 転写内容の編集 -->
  <section id="noteSec" class="hidden">
    <h2>2. OCR Result</h2>

    <div>
        <label for="transBox"><b>転写内容</b></label>
        <textarea id="transBox" readonly></textarea>
        <button id="toggleEdit">編集モードにする</button>
    </div>

    <!-- ユーザーへの質問を転写内容の下に移動 -->
    <div id="qArea" class="hidden">
        <h3>ユーザへの質問（確認ポイント）</h3>
        <ul id="questionList"></ul>
    </div>

    <!-- ページ送り -->
    <div id="pageNav" class="hidden" style="margin:1rem 0;">
      <button id="prevPage" disabled>◀ 前のページ</button>
      <span id="pageIndicator" style="margin:0 1rem;">0 / 0</span>
      <button id="nextPage" disabled>次のページ ▶</button>
    </div>

    <button id="btnMakePaper" class="hidden">論文案を生成</button>
  </section>

  <!-- ③ 生成された論文 -->
  <section id="paperSec" class="hidden">
    <h2>3. 生成された論文</h2>
    <textarea id="paperViewer" class="paper-area" readonly></textarea>

    <h3>フィードバック</h3>
    <textarea id="feedbackBox" placeholder="修正要望を入力"></textarea>
    <button id="btnRewrite">リライト</button>
  </section>

  <!-- ④ リライト後 -->
  <section id="revisedSec" class="hidden">
    <h2>4. リライト済み論文 <span id="rewriteCount">（1回目）</span></h2>
    <textarea id="revisedViewer" class="paper-area" readonly></textarea>
    
    <div id="rewriteActions">
      <div id="nextRewriteSection">
        <h3>さらにリライト</h3>
        <textarea id="nextFeedbackBox" placeholder="追加のフィードバックを入力"></textarea>
        <button id="btnNextRewrite">次のリライト</button>
      </div>
      
      <div id="manualEditSection">
        <h3>承認・修正</h3>
        <button id="btnManualEdit">この内容を元に承認・修正</button>
      </div>
    </div>
  </section>

  <!-- ⑤ 承認・修正 -->
  <section id="manualEditSec" class="hidden">
    <h2>5. 承認・修正</h2>
    <textarea id="manualEditBox" class="paper-area"></textarea>

    <!-- 著者情報入力 -->
    <div class="author-info-section">
      <h3>著者情報（オプション）</h3>
      <p>著者の既存文献を優先的に引用したい場合は、以下に入力してください：</p>
      <div class="author-inputs">
        <label>姓（Last Name）：</label>
        <input type="text" id="authorLastName" placeholder="例: Yamada">
        <label>名（First Name）：</label>
        <input type="text" id="authorFirstName" placeholder="例: Taro">
      </div>
      <div class="citation-options">
        <label>
          <input type="checkbox" id="prioritizeAuthor" checked>
          著者の既存文献を優先的に引用する
        </label>
        <label>
          <input type="checkbox" id="requireHighImpact">
          高インパクトファクター文献を優先（IF≥5）
        </label>
      </div>
    </div>

    <button id="btnAddCitations">文献の自動追加</button> <!-- 文献追加 -->
  </section>

  <!-- ⑥ 文献追加済み論文 -->
  <section id="citedPaperSec" class="hidden">
    <h2>6. 文献追加済み論文</h2>
    
    <div class="display-options">
      <label>表示形式：</label>
      <select id="citationFormat">
        <option value="endnote" selected>Endnoteタグ</option>
        <option value="numeric">引用番号</option>
      </select>
    </div>
    
    <textarea id="citedPaperViewer" class="paper-area" readonly></textarea>
    
    <div class="download-options">
      <button id="btnDownloadRIS">RISファイルをダウンロード</button>
      <button id="btnShowReport">詳細レポートを表示</button>
    </div>
    
    <div id="reportSection" class="hidden">
      <h3>引用詳細レポート</h3>
      <pre id="reportViewer"></pre>
    </div>
  </section>

  <style>
    .display-options, .download-options {
      margin: 10px 0;
    }
    #reportSection {
      margin-top: 20px;
    }
    #reportViewer {
      max-height: 300px;
      overflow: auto;
      background: #f5f5f5;
      padding: 10px;
      border: 1px solid #ddd;
    }
    .author-info-section {
      background: #f9f9f9;
      padding: 15px;
      margin: 15px 0;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .author-inputs {
      margin: 10px 0;
    }
    .author-inputs label {
      display: inline-block;
      width: 120px;
      margin-right: 10px;
    }
    .author-inputs input {
      width: 200px;
      padding: 5px;
      margin-right: 15px;
      margin-bottom: 10px;
    }
    .citation-options {
      margin: 10px 0;
    }
    .citation-options label {
      display: block;
      margin: 5px 0;
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  <script type="module" src="/static/js/auth.js"></script>
  <script type="module" src="/static/js/writer.js"></script>
</body>
</html>
