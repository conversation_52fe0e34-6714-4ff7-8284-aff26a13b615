<!-- /profile で配信 -->
<!DOCTYPE html>
<html lang="ja">
<head>
<meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1">
<title>My Profile</title>
<link rel="stylesheet" href="/static/css/base.css">
</head>
<body class="page">

<header class="topbar">
  <h1 class="logo"><a href="/" class="logo-link">MyAI<span>Agents</span></a></h1>
  <div id="userBox" class="badge" onclick="toggleMenu()">
    <span id="userName">guest</span><span id="slash"> / </span><span id="userRole">guest</span>
    <div id="menu" hidden>
      <a href="#" onclick="buy();return false;">Buy Credits</a>
      <a href="/profile.html">Profile</a>
      <a href="#" onclick="logout();return false;">Logout</a>
    </div>
  </div>
</header>

<main class="page-main">
  <h2 id="ttl">Profile</h2>

  <table id="pf_tbl">
    <tr><th id="th_user">User</th><td id="pf_name">–</td></tr>
    <tr><th id="th_role">Status</th><td id="pf_role">–</td></tr>
    <tr id="row_end" hidden>
      <th id="th_end">Subscription End</th>
      <td id="pf_end">–</td>
    </tr>
  </table>

  <!-- profile.htmlの任意の場所に -->
<button id="unsubscribeBtn" onclick="unsubscribe()">サブスクリプション解除</button>
</main>


<footer class="site-footer">
  <a href="/commercial_disclosure.html">Commercial Disclosure</a>
</footer>

<!-- すでに Toastify と auth.js は読み込まれている -->
<script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
<script src="/static/js/auth.js"></script>

<script>
/* ROLE_LABEL_EN / ROLE_LABEL_JP / JA は auth.js に存在するので再宣言しない */

async function fillProfile () {
  const u = await waitForUser();      // auth.js の関数（ヘッダー更新も含む）

  // 見出しの多言語
  $('ttl').textContent     = JA ? 'プロフィール' : 'Profile';
  $('th_user').textContent = JA ? 'ユーザー名'   : 'User';
  $('th_role').textContent = JA ? '会員種別'     : 'Status';
  $('th_end').textContent  = JA ? '契約終了日'   : 'Subscription End';

  if (!u) {                 // 未ログイン
    $('pf_name').textContent = 'guest';
    $('pf_role').textContent = JA ? 'ゲスト' : 'guest';
    $('row_end').hidden = true;
    return;
  }

  $('pf_name').textContent = u.username || u.email || u.id;
  $('pf_role').textContent = (JA ? ROLE_LABEL_JP : ROLE_LABEL_EN)[u.role ?? 0];

  if (u.subscription_end) {
    const dateStr = new Date(u.subscription_end)
        .toLocaleDateString(JA ? 'ja-JP' : 'en-US',
            { year: 'numeric', month: 'short', day: 'numeric' });
    $('pf_end').textContent = dateStr;
    $('row_end').hidden = false;
  } else {
    $('row_end').hidden = true;
  }

  if (u.subscription_end) {
    const dateStr = new Date(u.subscription_end)
        .toLocaleDateString(JA ? 'ja-JP' : 'en-US', { year: 'numeric', month: 'short', day: 'numeric' });
    let txt = dateStr;
    if (u.subscription_status === "cancel_at_period_end") {
      txt += JA ? "（解約予約済）" : " (pending cancellation)";
    }
    $('pf_end').textContent = txt;
    $('row_end').hidden = false;
  } else {
    $('row_end').hidden = true;
  }

  // サブスクリプション解除ボタンの表示
  $("unsubscribeBtn").style.display =
   (u.role === 2 && u.subscription_status === "active") ? "" : "none";

};


// Unsubscribe function
async function unsubscribe() {
  if (!confirm("Are you sure you want to unsubscribe?")) return;
  const token = localStorage.getItem("jwt");
  const res = await fetch("/billing/unsubscribe", {
    method: "POST",
    headers: { "Authorization": "Bearer " + token }
  });
  if (res.ok) {
    Toastify({text:"Done: UNSUBSCRIPTION",style:{background:"#198754"}}).showToast();
    // 以降再読み込み／UIロール更新など
    location.reload();
  } else {
    Toastify({text:"unsubscription FAILED!",style:{background:"#d9534f"}}).showToast();
  }
}

document.addEventListener('DOMContentLoaded', fillProfile);

</script>

</body>
</html>

