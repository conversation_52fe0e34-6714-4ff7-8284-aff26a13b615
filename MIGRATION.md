# マイグレーションガイド v1.0 → v2.0

## 概要

このガイドでは、CITER and other AI Agents v1.0からv2.0への移行手順を説明します。

## 主な変更点

### 1. ディレクトリ構造の変更

**v1.0 (旧構造)**:
```
citer2/
├── agent/
│   ├── main.py
│   ├── auth.py
│   ├── models.py
│   ├── pipeline.py
│   ├── ocr_service.py
│   └── routes/
└── static/
```

**v2.0 (新構造)**:
```
citer2/
├── app/
│   ├── main.py
│   ├── config/
│   ├── shared/
│   ├── features/
│   └── api/
└── static/
```

### 2. インポートパスの変更

| v1.0 | v2.0 |
|------|------|
| `from agent.models import User` | `from app.shared.auth.models import User` |
| `from agent.auth import current_user` | `from app.shared.auth.backends import current_user` |
| `from agent.pipeline import run_pipeline` | `from app.features.citer2.services import citer_service` |
| `from agent.ocr_service import OCRService` | `from app.features.writer.ocr import ocr_service` |

### 3. 設定管理の変更

**v1.0**: 環境変数を直接使用
```python
SECRET_KEY = os.getenv("SECRET_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
```

**v2.0**: Pydantic Settings使用
```python
from app.config.settings import settings
secret_key = settings.secret_key
openai_api_key = settings.openai_api_key
```

## 移行手順

### ステップ1: 新しい構造の準備

1. 新しいディレクトリ構造を作成
2. 既存コードを新しい場所にコピー
3. インポートパスを更新

### ステップ2: 設定の移行

1. `.env`ファイルの確認・更新
2. 新しい設定クラスの利用
3. 機能別設定の分離

### ステップ3: データベースの移行

**注意**: データベーススキーマに変更があります

1. 既存データのバックアップ
```bash
# SQLiteの場合
cp db.sqlite3 db_backup.sqlite3
```

2. 新しいスキーマでテーブル作成
```bash
# 新しいアプリケーションでテーブル作成
python -c "from app.config.database import create_tables_async; import asyncio; asyncio.run(create_tables_async())"
```

3. データ移行スクリプトの実行（必要に応じて）

### ステップ4: API エンドポイントの確認

一部のAPIエンドポイントが変更されています：

| v1.0 | v2.0 |
|------|------|
| `/search` | `/api/citer2/search` |
| `/note` | `/api/writer/note` |
| `/auth/jwt/login` | `/auth/jwt/login` (変更なし) |

### ステップ5: フロントエンドの更新

静的ファイル（HTML/JS）のAPIエンドポイント呼び出しを更新：

```javascript
// v1.0
fetch('/search', { ... })

// v2.0
fetch('/api/citer2/search', { ... })
```

## 互換性の注意点

### 1. 破壊的変更

- **APIエンドポイント**: 一部のパスが変更
- **データベーススキーマ**: 新しいテーブル構造
- **設定管理**: 環境変数の読み込み方法変更

### 2. 非破壊的変更

- **認証システム**: 既存のJWTトークンは引き続き有効
- **静的ファイル**: `/static`パスは変更なし
- **基本機能**: CITER2、Writer機能は引き続き利用可能

## トラブルシューティング

### よくある問題

#### 1. インポートエラー
```
ModuleNotFoundError: No module named 'agent'
```

**解決方法**: インポートパスを新しい構造に更新

#### 2. 設定エラー
```
ValidationError: Extra inputs are not permitted
```

**解決方法**: 設定クラスに`extra = "ignore"`を追加済み

#### 3. データベース接続エラー
```
sqlalchemy.exc.OperationalError
```

**解決方法**: `DATABASE_URL`環境変数を確認

### デバッグ方法

1. **ログレベルの設定**:
```bash
LOG_LEVEL=DEBUG python -m uvicorn app.main:app
```

2. **設定値の確認**:
```python
from app.config.settings import settings
print(settings.dict())
```

3. **データベース接続テスト**:
```python
from app.config.database import get_async_db
# 接続テスト実行
```

## ロールバック手順

問題が発生した場合のロールバック：

1. **アプリケーションの停止**
2. **データベースの復元**:
```bash
cp db_backup.sqlite3 db.sqlite3
```
3. **v1.0コードの復元**
4. **v1.0サーバーの起動**

## 移行後の確認事項

### 1. 機能テスト
- [ ] ユーザー認証
- [ ] 文献検索（CITER2）
- [ ] OCR処理（Writer）
- [ ] API ドキュメント表示

### 2. パフォーマンステスト
- [ ] レスポンス時間
- [ ] メモリ使用量
- [ ] データベース接続

### 3. セキュリティテスト
- [ ] 認証・認可
- [ ] API アクセス制限
- [ ] 環境変数の保護

## サポート

移行に関する問題や質問がある場合：

1. **ログの確認**: アプリケーションログを確認
2. **設定の検証**: 環境変数と設定値を確認
3. **段階的移行**: 機能ごとに段階的に移行
4. **テスト環境**: 本番環境移行前にテスト環境で検証

## 移行完了後の利点

- **保守性向上**: 機能別の明確な分離
- **拡張性向上**: 新機能の追加が容易
- **テスト性向上**: 独立したテストが可能
- **設定管理**: 統一された設定管理
- **ドキュメント**: 充実したAPI ドキュメント
