"""
メインアプリケーションのテスト
"""
import pytest
from fastapi.testclient import TestClient


def test_root_endpoint(client: TestClient):
    """ルートエンドポイントテスト"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "status" in data
    assert data["status"] == "running"


def test_health_check(client: TestClient):
    """ヘルスチェックテスト"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["version"] == "2.0.0"
