"""
設定モジュールのテスト
"""
import pytest
from app.config.settings import settings, citer_settings, writer_settings


def test_settings_initialization():
    """設定の初期化テスト"""
    assert settings.app_name == "CITER and other AI Agents"
    assert settings.jwt_lifetime_seconds == 24 * 60 * 60
    assert isinstance(settings.debug, bool)


def test_citer_settings():
    """CITER2設定テスト"""
    assert citer_settings.default_hits == 10
    assert citer_settings.default_threshold == 4
    assert citer_settings.max_workers == 4


def test_writer_settings():
    """Writer設定テスト"""
    assert writer_settings.default_ocr_method == "azure"
    assert writer_settings.ocr_preprocess is True
