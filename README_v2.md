# CITER and other AI Agents v2.0

機能別に整理されたモジュール構造に再設計されたAIエージェントプラットフォーム

## 🏗️ アーキテクチャ概要

### 新しいディレクトリ構造

```
citer2/
├── app/                          # アプリケーションのメインディレクトリ
│   ├── main.py                   # FastAPIアプリケーションのエントリーポイント
│   ├── config/                   # 設定管理
│   │   ├── settings.py           # 環境変数・設定の統一管理
│   │   └── database.py           # データベース設定
│   ├── shared/                   # 共通機能
│   │   ├── auth/                 # 認証関連
│   │   ├── database/             # データベース関連
│   │   ├── models/               # 共通データモデル
│   │   └── utils/                # 共通ユーティリティ
│   ├── features/                 # 機能別モジュール
│   │   ├── citer2/               # 文献検索機能
│   │   ├── writer/               # 論文作成機能
│   │   ├── todos/                # Gmail ToDo機能
│   │   └── billing/              # 課金機能
│   └── api/                      # API層
│       ├── dependencies.py       # API共通依存関係
│       └── routes/               # APIルート
├── static/                       # 静的ファイル
├── tests/                        # テスト
└── requirements.txt
```

## 🚀 クイックスタート

### 1. 環境設定

```bash
# 仮想環境の作成と有効化
python -m venv venv
source venv/bin/activate  # Linux/Mac
# または
venv\Scripts\activate     # Windows

# 依存関係のインストール
pip install -r requirements.txt
```

### 2. 環境変数の設定

`.env`ファイルを作成して必要な環境変数を設定：

```env
# 基本設定
SECRET_KEY=your-secret-key-here
SESSION_SECRET=your-session-secret-here
DATABASE_URL=sqlite:///./db.sqlite3

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Azure Computer Vision (オプション)
AZURE_CV_ENDPOINT=your-azure-endpoint
AZURE_CV_KEY=your-azure-key

# PubMed API (オプション)
NCBI_API_KEY=your-ncbi-api-key
NCBI_EMAIL=<EMAIL>

# Stripe (オプション)
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
```

### 3. アプリケーションの起動

```bash
# 開発サーバーの起動
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

アプリケーションは http://localhost:8000 でアクセス可能です。

## 📚 主要機能

### 🔍 CITER2 - 文献検索エージェント
- 自動文献検索と引用生成
- PubMed統合
- 複数の検索戦略
- RIS形式エクスポート

### ✍️ Writer - 論文作成エージェント
- OCR機能（手書きノート認識）
- 自動論文生成
- 文献自動追加
- セクション識別

### 📧 Gmail ToDo エージェント
- Gmail自動解析
- タスク抽出と管理
- カテゴリ分類

### 💳 課金システム
- Stripe統合
- 使用制限管理
- サブスクリプション管理

## 🔧 開発者向け情報

### アーキテクチャの特徴

1. **機能別分離**: 各機能が独立したモジュールに分離
2. **共通機能の再利用**: 認証、データベースなどの共通機能を一箇所に集約
3. **拡張性**: 新機能を`features/`に追加するだけで済む
4. **テスタビリティ**: 各モジュールが独立してテスト可能
5. **保守性**: 機能ごとに開発・保守が可能

### 設定管理

- `app/config/settings.py`: 全ての設定を統一管理
- 機能別設定クラス（CiterSettings, WriterSettings等）
- 環境変数の自動読み込み

### データベース

- SQLAlchemy 2.0対応
- 非同期サポート
- 開発環境：SQLite、本番環境：PostgreSQL対応

### 認証システム

- FastAPI-Users統合
- JWT認証
- Google OAuth2
- ロールベースアクセス制御

## 🧪 テスト

```bash
# テストの実行
pytest

# カバレッジ付きテスト
pytest --cov=app

# 特定のテストファイル
pytest tests/unit/test_config.py
```

## 📖 API ドキュメント

アプリケーション起動後、以下のURLでAPIドキュメントを確認できます：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🔄 マイグレーション

既存のv1.0からv2.0への移行については、`MIGRATION.md`を参照してください。

## 🤝 コントリビューション

1. フォークしてブランチを作成
2. 機能を実装
3. テストを追加
4. プルリクエストを作成

## 📄 ライセンス

このプロジェクトは MIT ライセンスの下で公開されています。

## 🆕 v2.0の主な変更点

- 機能別モジュール構造への再設計
- 設定管理の統一
- 依存関係の整理
- テスト環境の整備
- API構造の統一
- ドキュメントの充実
