"""
API共通依存関係
レート制限、認証、バリデーションなど
"""
import time
from typing import Dict, Any, Optional
from fastapi import Depends, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.database import get_async_db
from app.shared.auth.models import User
from app.shared.auth.backends import current_active_user


class RateLimiter:
    """レート制限クラス"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests: Dict[str, list] = {}
    
    def is_allowed(self, key: str) -> bool:
        """レート制限チェック"""
        now = time.time()
        
        if key not in self.requests:
            self.requests[key] = []
        
        # 古いリクエストを削除
        self.requests[key] = [
            req_time for req_time in self.requests[key]
            if now - req_time < self.window_seconds
        ]
        
        # 制限チェック
        if len(self.requests[key]) >= self.max_requests:
            return False
        
        # 新しいリクエストを記録
        self.requests[key].append(now)
        return True


# グローバルレート制限インスタンス
rate_limiter = RateLimiter()


async def check_rate_limit(request: Request):
    """レート制限チェック依存関数"""
    client_ip = request.client.host
    
    if not rate_limiter.is_allowed(client_ip):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded. Please try again later."
        )
    
    return True


async def get_current_user_optional(
    user: Optional[User] = Depends(current_active_user)
) -> Optional[User]:
    """オプショナルなユーザー取得"""
    return user


async def validate_json_body(request: Request) -> Dict[str, Any]:
    """JSONボディのバリデーション"""
    try:
        body = await request.json()
        return body
    except Exception:
        raise HTTPException(400, "Invalid JSON body")


class PaginationParams:
    """ページネーションパラメータ"""
    
    def __init__(self, page: int = 1, size: int = 20):
        if page < 1:
            raise HTTPException(400, "Page must be >= 1")
        if size < 1 or size > 100:
            raise HTTPException(400, "Size must be between 1 and 100")
        
        self.page = page
        self.size = size
        self.offset = (page - 1) * size


def get_pagination_params(page: int = 1, size: int = 20) -> PaginationParams:
    """ページネーションパラメータ取得"""
    return PaginationParams(page, size)
