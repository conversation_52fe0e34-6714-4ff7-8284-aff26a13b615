"""
CITER2 API
文献検索、引用生成関連のエンドポイント
"""
import uuid
import json
import logging
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.database import get_async_db
from app.shared.auth.models import User
from app.shared.auth.dependencies import citer_usage_limit, record_usage
from app.features.citer2.schemas import (
    SearchRequest, SearchResponse, JobStatusResponse, 
    SimpleSearchRequest, SimpleSearchResponse,
    UserPreferencesSchema
)
from app.features.citer2.services import citer_service

logger = logging.getLogger(__name__)

# ルーター作成
router = APIRouter(prefix="/citer2", tags=["citer2"])

# ジョブ管理用辞書（本来はRedisなどを使用）
jobs: Dict[str, dict] = {}


@router.post("/search", response_model=SearchResponse)
async def start_literature_search(
    request: SearchRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(citer_usage_limit),
    session: AsyncSession = Depends(get_async_db)
):
    """文献検索開始"""
    try:
        job_id = str(uuid.uuid4())
        
        # ジョブ初期化
        jobs[job_id] = {
            "status": "pending",
            "user_id": str(user.id),
            "request": request.dict(),
            "result": None,
            "error": None
        }
        
        # バックグラウンドで検索実行
        background_tasks.add_task(
            _execute_search,
            job_id,
            request,
            str(user.id)
        )
        
        # 使用記録
        await record_usage(
            str(user.id),
            "citer2",
            "search",
            {"hits": request.hits, "model": request.model},
            session
        )
        
        return SearchResponse(
            job_id=job_id,
            status="pending",
            message="文献検索を開始しました"
        )
        
    except Exception as e:
        logger.error(f"Search start failed: {e}")
        raise HTTPException(500, f"検索開始に失敗しました: {str(e)}")


@router.get("/jobs/{job_id}/status", response_model=JobStatusResponse)
async def get_job_status(job_id: str):
    """ジョブステータス取得"""
    if job_id not in jobs:
        raise HTTPException(404, "Job not found")
    
    job = jobs[job_id]
    return JobStatusResponse(
        job_id=job_id,
        status=job["status"],
        result=job.get("result"),
        error=job.get("error")
    )


@router.get("/jobs/{job_id}/stream")
async def stream_job_progress(job_id: str):
    """ジョブ進捗のストリーミング"""
    if job_id not in jobs:
        raise HTTPException(404, "Job not found")
    
    async def generate():
        while True:
            if job_id not in jobs:
                break
            
            job = jobs[job_id]
            yield f"data: {json.dumps(job, ensure_ascii=False)}\n\n"
            
            if job["status"] in ["completed", "failed"]:
                break
            
            await asyncio.sleep(1)
    
    return StreamingResponse(
        generate(),
        media_type="text/plain",
        headers={"Cache-Control": "no-cache"}
    )


@router.post("/simple_search", response_model=SimpleSearchResponse)
async def simple_literature_search(
    request: SimpleSearchRequest,
    user: User = Depends(citer_usage_limit),
    session: AsyncSession = Depends(get_async_db)
):
    """シンプル文献検索（Citer2用）"""
    try:
        # 簡略化された検索処理
        from app.features.citer2.pipeline import (
            split_sentences, clean_duplicate_citations, generate_ris_content
        )
        
        # 文分割
        sentences = split_sentences(request.text)
        
        # 各文に対して簡単な引用追加（実装簡略化）
        cited_text = request.text  # 実際は引用を追加
        
        # RIS生成
        ris_content = ""  # 実際はRIS形式で生成
        
        # 使用記録
        await record_usage(
            str(user.id),
            "citer2",
            "simple_search",
            {"hits": request.hits, "model": request.model},
            session
        )
        
        return SimpleSearchResponse(
            cited_text=cited_text,
            ris_content=ris_content,
            statistics={
                "total_sentences": len(sentences),
                "total_citations": 0
            }
        )
        
    except Exception as e:
        logger.error(f"Simple search failed: {e}")
        raise HTTPException(500, f"検索に失敗しました: {str(e)}")


@router.get("/preferences", response_model=UserPreferencesSchema)
async def get_user_preferences(
    user: User = Depends(citer_usage_limit),
    session: AsyncSession = Depends(get_async_db)
):
    """ユーザー設定取得"""
    # 実装簡略化 - デフォルト値を返す
    return UserPreferencesSchema()


@router.put("/preferences", response_model=UserPreferencesSchema)
async def update_user_preferences(
    preferences: UserPreferencesSchema,
    user: User = Depends(citer_usage_limit),
    session: AsyncSession = Depends(get_async_db)
):
    """ユーザー設定更新"""
    # 実装簡略化
    return preferences


async def _execute_search(job_id: str, request: SearchRequest, user_id: str):
    """バックグラウンドで文献検索実行"""
    try:
        jobs[job_id]["status"] = "running"
        
        # 進捗コールバック
        def progress_callback(message: str):
            if job_id in jobs:
                jobs[job_id]["progress"] = json.loads(message)
        
        # 検索実行
        result = await citer_service.search_literature(request, progress_callback)
        
        jobs[job_id]["status"] = "completed"
        jobs[job_id]["result"] = result.dict()
        
    except Exception as e:
        logger.error(f"Search execution failed: {e}")
        jobs[job_id]["status"] = "failed"
        jobs[job_id]["error"] = str(e)
