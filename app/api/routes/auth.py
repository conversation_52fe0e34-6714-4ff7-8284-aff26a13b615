"""
認証API
ユーザー認証、OAuth、JWT関連のエンドポイント
"""
import os
import uuid
import datetime as dt
import jwt as jwt_lib
import logging
from typing import Optional, Dict

from fastapi import APIRouter, Depends, Request, HTTPException, Query, Response
from fastapi.responses import RedirectResponse, JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.config.settings import settings
from app.config.database import get_async_db
from app.shared.auth.backends import oauth, fastapi_users, jwt_backend, GMAIL_SCOPES, CALENDAR_SCOPES
from app.shared.auth.models import User
from app.shared.auth.schemas import UserCreate, UserRead, UserUpdate
from app.shared.auth.manager import get_user_manager

logger = logging.getLogger(__name__)

# ルーター作成
router = APIRouter(prefix="/auth", tags=["auth"])

# FastAPI-Usersの標準ルートを追加
router.include_router(
    fastapi_users.get_auth_router(jwt_backend),
    prefix="/jwt",
)

router.include_router(
    fastapi_users.get_register_router(UserRead, UserCreate),
)

router.include_router(
    fastapi_users.get_reset_password_router(),
)

router.include_router(
    fastapi_users.get_verify_router(UserRead),
)

router.include_router(
    fastapi_users.get_users_router(UserRead, UserUpdate),
    prefix="/users",
)

# Google OAuth ルーター
google_router = APIRouter(prefix="/google", tags=["oauth"])


@google_router.get("/login")
async def google_sso_login(request: Request):
    """Google SSOログイン開始"""
    redirect_uri = "http://myaiagents.duckdns.org/auth/google/callback"
    return await oauth.google.authorize_redirect(
        request,
        redirect_uri,
        scope=" ".join(BASE_SCOPES),
        access_type="offline",
        prompt="select_account"
    )


@google_router.get("/gmail")
async def google_gmail_connect(request: Request):
    """Gmail接続開始"""
    redirect_uri = "http://myaiagents.duckdns.org/auth/google/callback"
    request.session["oauth_service"] = "gmail"
    return await oauth.google.authorize_redirect(
        request,
        redirect_uri,
        scope=" ".join(GMAIL_SCOPES),
        access_type="offline",
        prompt="consent"
    )


@google_router.get("/calendar")
async def google_calendar_connect(request: Request):
    """Calendar接続開始"""
    redirect_uri = "http://myaiagents.duckdns.org/auth/google/callback"
    request.session["oauth_service"] = "calendar"
    return await oauth.google.authorize_redirect(
        request,
        redirect_uri,
        scope=" ".join(CALENDAR_SCOPES),
        access_type="offline",
        prompt="consent"
    )


@google_router.get("/callback")
async def google_oauth_callback(
    request: Request,
    svc: str = Query(default="sso"),
    session: AsyncSession = Depends(get_async_db),
    user_manager = Depends(get_user_manager)
):
    """Google OAuthコールバック"""
    try:
        # セッションからサービス情報を取得
        svc = request.session.pop("oauth_service", svc)

        # 1. トークン取得
        token = await oauth.google.authorize_access_token(request)
        if not token:
            raise HTTPException(400, "Failed to get access token")
        
        # 2. ユーザー情報取得
        userinfo = (await oauth.google.get(
            "https://openidconnect.googleapis.com/v1/userinfo",
            token=token,
        )).json()
        email = userinfo["email"]
        
        # 3. ユーザー取得または作成
        if svc == "sso":
            try:
                user = await user_manager.get_by_email(email)
            except Exception:
                # ユーザーが存在しない場合は作成
                dummy_pass = jwt_lib.encode(
                    {"sub": userinfo["sub"],
                     "exp": dt.datetime.utcnow() + dt.timedelta(days=365)},
                    settings.secret_key, algorithm="HS256"
                )
                user = await user_manager.create(UserCreate(
                    email=email,
                    password=dummy_pass,
                    is_verified=True,
                    role=0,
                ))
        else:
            # Gmail/Calendar接続の場合は既存ユーザーが必要
            user_id = request.session.get("user_id")
            if not user_id:
                raise HTTPException(400, "User not logged in")
            
            stmt = select(User).where(User.id == uuid.UUID(user_id))
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()
            if not user:
                raise HTTPException(404, "User not found")
        
        # 4. トークン保存
        creds_common = {
            "access_token": token["access_token"],
            "token_type": token.get("token_type", "Bearer"),
            "expires_at": token.get("expires_at"),
            "scope": token.get("scope", ""),
        }
        
        if svc == "gmail":
            refresh_token = token.get("refresh_token")
            if not refresh_token and user.gmail_tokens:
                refresh_token = user.gmail_tokens.get("refresh_token")
            
            if not refresh_token:
                raise HTTPException(400, "refresh_token が取得できませんでした。再度 Connect してください。")
            
            user.gmail_tokens = {**creds_common, "refresh_token": refresh_token}
        
        elif svc == "calendar":
            refresh_token = token.get("refresh_token")
            if not refresh_token and user.calendar_tokens:
                refresh_token = user.calendar_tokens.get("refresh_token")
            
            if not refresh_token:
                raise HTTPException(400, "refresh_token が取得できませんでした。再度 Connect してください。")
            
            user.calendar_tokens = {**creds_common, "refresh_token": refresh_token}
        
        await session.commit()
        
        # 5. JWT発行してリダイレクト
        new_jwt = await jwt_backend.get_strategy().write_token(user)
        dest = "/todos.html" if svc in ("gmail", "calendar") else "/"
        return RedirectResponse(f"{dest}?jwt={new_jwt}")
        
    except Exception as e:
        logger.error(f"OAuth callback error: {e}")
        raise HTTPException(500, f"OAuth callback failed: {str(e)}")


# Google OAuthルーターを追加
router.include_router(google_router)


@router.get("/me")
async def get_current_user_info(user: User = Depends(fastapi_users.current_user())):
    """現在のユーザー情報取得"""
    return UserRead.from_orm(user)


@router.post("/logout")
async def logout():
    """ログアウト"""
    return {"message": "Logged out successfully"}
