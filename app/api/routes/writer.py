"""
Writer API
OCR、論文生成関連のエンドポイント
"""
import uuid
import logging
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Body, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.database import get_async_db
from app.shared.auth.models import User
from app.shared.auth.dependencies import writer_usage_limit, record_usage
from app.features.writer.schemas import (
    OcrRequest, OcrResponse, OcrJobStatus,
    PaperGenerationRequest, PaperRewriteRequest, CitationAddRequest,
    PaperResponse, PaperContent, WriterPreferencesSchema,
    SectionIdentificationRequest, SectionIdentificationResponse
)
from app.features.writer.ocr import ocr_service

logger = logging.getLogger(__name__)

# ルーター作成
router = APIRouter(prefix="/writer", tags=["writer"])

# ジョブ管理用辞書（本来はRedisなどを使用）
ocr_jobs: Dict[str, dict] = {}
paper_jobs: Dict[str, dict] = {}


@router.post("/note", response_model=OcrResponse)
async def upload_note(
    files: List[UploadFile] = File(...),
    method: str = Form("azure"),
    output_format: str = Form("layout"),
    preprocess: bool = Form(True),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    user: User = Depends(writer_usage_limit),
    session: AsyncSession = Depends(get_async_db)
):
    """実験ノート画像のOCR処理"""
    try:
        if not files:
            raise HTTPException(400, "ファイルがアップロードされていません")
        
        job_id = str(uuid.uuid4())
        
        # ジョブ初期化
        ocr_jobs[job_id] = {
            "status": "pending",
            "user_id": str(user.id),
            "file_count": len(files),
            "completed_count": 0,
            "results": [],
            "error": None
        }
        
        # バックグラウンドでOCR実行
        background_tasks.add_task(
            _execute_ocr,
            job_id,
            files,
            method,
            output_format,
            preprocess
        )
        
        # 使用記録
        await record_usage(
            str(user.id),
            "writer",
            "ocr",
            {"file_count": len(files), "method": method},
            session
        )
        
        return OcrResponse(
            job_id=job_id,
            status="pending",
            file_count=len(files),
            message="OCR処理を開始しました"
        )
        
    except Exception as e:
        logger.error(f"OCR upload failed: {e}")
        raise HTTPException(500, f"OCR処理の開始に失敗しました: {str(e)}")


@router.get("/note/{job_id}/status", response_model=OcrJobStatus)
async def get_ocr_status(job_id: str):
    """OCRジョブステータス取得"""
    if job_id not in ocr_jobs:
        raise HTTPException(404, "Job not found")
    
    job = ocr_jobs[job_id]
    return OcrJobStatus(
        job_id=job_id,
        status=job["status"],
        file_count=job["file_count"],
        completed_count=job["completed_count"],
        results=job.get("results", []),
        error=job.get("error")
    )


@router.post("/paper/generate", response_model=PaperResponse)
async def generate_paper(
    request: PaperGenerationRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(writer_usage_limit),
    session: AsyncSession = Depends(get_async_db)
):
    """論文生成"""
    try:
        job_id = str(uuid.uuid4())
        
        # ジョブ初期化
        paper_jobs[job_id] = {
            "status": "pending",
            "user_id": str(user.id),
            "type": "generate",
            "paper_id": request.paper_id,
            "result": None,
            "error": None
        }
        
        # バックグラウンドで論文生成実行
        background_tasks.add_task(
            _execute_paper_generation,
            job_id,
            request
        )
        
        # 使用記録
        await record_usage(
            str(user.id),
            "writer",
            "generate",
            {"model": request.model, "language": request.language},
            session
        )
        
        return PaperResponse(
            paper_id=request.paper_id,
            status="pending",
            message="論文生成を開始しました"
        )
        
    except Exception as e:
        logger.error(f"Paper generation failed: {e}")
        raise HTTPException(500, f"論文生成の開始に失敗しました: {str(e)}")


@router.post("/paper/rewrite", response_model=PaperResponse)
async def rewrite_paper(
    request: PaperRewriteRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(writer_usage_limit),
    session: AsyncSession = Depends(get_async_db)
):
    """論文リライト"""
    try:
        job_id = str(uuid.uuid4())
        
        # ジョブ初期化
        paper_jobs[job_id] = {
            "status": "pending",
            "user_id": str(user.id),
            "type": "rewrite",
            "paper_id": request.paper_id,
            "result": None,
            "error": None
        }
        
        # バックグラウンドでリライト実行
        background_tasks.add_task(
            _execute_paper_rewrite,
            job_id,
            request
        )
        
        # 使用記録
        await record_usage(
            str(user.id),
            "writer",
            "rewrite",
            {"model": request.model, "type": request.rewrite_type},
            session
        )
        
        return PaperResponse(
            paper_id=request.paper_id,
            status="pending",
            message="論文リライトを開始しました"
        )
        
    except Exception as e:
        logger.error(f"Paper rewrite failed: {e}")
        raise HTTPException(500, f"論文リライトの開始に失敗しました: {str(e)}")


@router.post("/paper/add_citations", response_model=PaperResponse)
async def add_citations(
    request: CitationAddRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(writer_usage_limit),
    session: AsyncSession = Depends(get_async_db)
):
    """文献自動追加"""
    try:
        job_id = str(uuid.uuid4())
        
        # ジョブ初期化
        paper_jobs[job_id] = {
            "status": "pending",
            "user_id": str(user.id),
            "type": "add_citations",
            "paper_id": request.paper_id,
            "result": None,
            "error": None
        }
        
        # バックグラウンドで文献追加実行
        background_tasks.add_task(
            _execute_citation_addition,
            job_id,
            request
        )
        
        # 使用記録
        await record_usage(
            str(user.id),
            "writer",
            "add_citations",
            {"hits": request.hits, "model": request.model},
            session
        )
        
        return PaperResponse(
            paper_id=request.paper_id,
            status="pending",
            message="文献追加を開始しました"
        )
        
    except Exception as e:
        logger.error(f"Citation addition failed: {e}")
        raise HTTPException(500, f"文献追加の開始に失敗しました: {str(e)}")


@router.post("/identify_sections", response_model=SectionIdentificationResponse)
async def identify_sections(
    request: SectionIdentificationRequest,
    user: User = Depends(writer_usage_limit)
):
    """セクション識別"""
    try:
        # 簡略化実装 - 実際はGPTを使用してセクション識別
        sections = {
            "introduction": "イントロダクション部分",
            "methods": "方法部分", 
            "results": "結果部分",
            "discussion": "考察部分"
        }
        
        return SectionIdentificationResponse(
            sections=sections,
            format=request.format
        )
        
    except Exception as e:
        logger.error(f"Section identification failed: {e}")
        raise HTTPException(500, f"セクション識別に失敗しました: {str(e)}")


@router.get("/preferences", response_model=WriterPreferencesSchema)
async def get_writer_preferences(
    user: User = Depends(writer_usage_limit),
    session: AsyncSession = Depends(get_async_db)
):
    """Writer設定取得"""
    # 実装簡略化 - デフォルト値を返す
    return WriterPreferencesSchema()


@router.put("/preferences", response_model=WriterPreferencesSchema)
async def update_writer_preferences(
    preferences: WriterPreferencesSchema,
    user: User = Depends(writer_usage_limit),
    session: AsyncSession = Depends(get_async_db)
):
    """Writer設定更新"""
    # 実装簡略化
    return preferences


async def _execute_ocr(
    job_id: str,
    files: List[UploadFile],
    method: str,
    output_format: str,
    preprocess: bool
):
    """バックグラウンドでOCR実行"""
    try:
        ocr_jobs[job_id]["status"] = "processing"
        results = []
        
        for idx, file in enumerate(files):
            try:
                # ファイル読み込み
                content = await file.read()
                
                # OCR実行
                result = ocr_service.extract_text(content, method, output_format)
                
                results.append({
                    "file_index": idx,
                    "filename": file.filename,
                    "method": method,
                    "text": result.get("text", ""),
                    "confidence": result.get("confidence"),
                    "success": result.get("success", False),
                    "error": result.get("error")
                })
                
                ocr_jobs[job_id]["completed_count"] = idx + 1
                
            except Exception as e:
                logger.error(f"OCR failed for file {idx}: {e}")
                results.append({
                    "file_index": idx,
                    "filename": file.filename,
                    "method": method,
                    "text": "",
                    "success": False,
                    "error": str(e)
                })
        
        ocr_jobs[job_id]["status"] = "completed"
        ocr_jobs[job_id]["results"] = results
        
    except Exception as e:
        logger.error(f"OCR execution failed: {e}")
        ocr_jobs[job_id]["status"] = "failed"
        ocr_jobs[job_id]["error"] = str(e)


async def _execute_paper_generation(job_id: str, request: PaperGenerationRequest):
    """バックグラウンドで論文生成実行"""
    try:
        paper_jobs[job_id]["status"] = "processing"
        
        # 実装簡略化 - 実際は既存のwriter_svc.pyから移行
        result = {
            "paper_id": request.paper_id,
            "content": "生成された論文内容",
            "sections": {},
            "citations": []
        }
        
        paper_jobs[job_id]["status"] = "completed"
        paper_jobs[job_id]["result"] = result
        
    except Exception as e:
        logger.error(f"Paper generation execution failed: {e}")
        paper_jobs[job_id]["status"] = "failed"
        paper_jobs[job_id]["error"] = str(e)


async def _execute_paper_rewrite(job_id: str, request: PaperRewriteRequest):
    """バックグラウンドで論文リライト実行"""
    try:
        paper_jobs[job_id]["status"] = "processing"
        
        # 実装簡略化
        result = {
            "paper_id": request.paper_id,
            "content": "リライトされた論文内容",
            "changes": []
        }
        
        paper_jobs[job_id]["status"] = "completed"
        paper_jobs[job_id]["result"] = result
        
    except Exception as e:
        logger.error(f"Paper rewrite execution failed: {e}")
        paper_jobs[job_id]["status"] = "failed"
        paper_jobs[job_id]["error"] = str(e)


async def _execute_citation_addition(job_id: str, request: CitationAddRequest):
    """バックグラウンドで文献追加実行"""
    try:
        paper_jobs[job_id]["status"] = "processing"
        
        # 実装簡略化
        result = {
            "paper_id": request.paper_id,
            "cited_text": "引用が追加された論文内容",
            "citations": [],
            "bibliography": ""
        }
        
        paper_jobs[job_id]["status"] = "completed"
        paper_jobs[job_id]["result"] = result
        
    except Exception as e:
        logger.error(f"Citation addition execution failed: {e}")
        paper_jobs[job_id]["status"] = "failed"
        paper_jobs[job_id]["error"] = str(e)
