"""
統一設定管理
環境変数とアプリケーション設定を一元管理
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """アプリケーション設定"""
    
    # === 基本設定 ===
    app_name: str = "CITER and other AI Agents"
    debug: bool = False
    
    # === セキュリティ ===
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    session_secret: str = os.getenv("SESSION_SECRET", "your-session-secret-here")
    
    # === データベース ===
    database_url: str = os.getenv("DATABASE_URL", "sqlite:///./db.sqlite3")
    
    # === 認証 ===
    jwt_lifetime_seconds: int = 24 * 60 * 60  # 24時間
    
    # === Google OAuth ===
    google_client_id: str = os.getenv("GOOGLE_CLIENT_ID", "")
    google_client_secret: str = os.getenv("GOOGLE_CLIENT_SECRET", "")
    
    # === OpenAI ===
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    
    # === Azure Computer Vision ===
    azure_cv_endpoint: str = os.getenv("AZURE_CV_ENDPOINT", "")
    azure_cv_key: str = os.getenv("AZURE_CV_KEY", "")
    
    # === Stripe ===
    stripe_secret_key: str = os.getenv("STRIPE_SECRET_KEY", "")
    stripe_publishable_key: str = os.getenv("STRIPE_PUBLISHABLE_KEY", "")
    stripe_webhook_secret: str = os.getenv("STRIPE_WEBHOOK_SECRET", "")
    
    # === PubMed API ===
    ncbi_api_key: str = os.getenv("NCBI_API_KEY", "")
    ncbi_email: str = os.getenv("NCBI_EMAIL", "")
    
    # === ジョブ管理 ===
    job_ttl_seconds: int = int(os.getenv("JOB_TTL_SEC", "600"))
    
    # === ログ設定 ===
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    debug_progress: bool = os.getenv("DEBUG_PROGRESS") == "1"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


class CiterSettings(BaseSettings):
    """CITER2固有の設定"""
    
    # === 検索設定 ===
    default_hits: int = 10
    default_threshold: int = 4
    default_max_refs: int = 3
    default_model: str = "o3"
    
    # === レート制限 ===
    ncbi_rate_limit: float = 0.13  # API keyありの場合
    max_workers: int = 4
    
    class Config:
        env_prefix = "CITER_"


class WriterSettings(BaseSettings):
    """Writer固有の設定"""
    
    # === OCR設定 ===
    default_ocr_method: str = "azure"
    ocr_preprocess: bool = True
    
    # === 論文生成設定 ===
    default_paper_model: str = "o3"
    
    class Config:
        env_prefix = "WRITER_"


class TodosSettings(BaseSettings):
    """Gmail ToDo固有の設定"""
    
    # === Gmail API設定 ===
    gmail_scopes: list = [
        "https://www.googleapis.com/auth/gmail.readonly",
        "https://www.googleapis.com/auth/calendar"
    ]
    
    class Config:
        env_prefix = "TODOS_"


class BillingSettings(BaseSettings):
    """課金機能固有の設定"""
    
    # === 使用制限 ===
    free_daily_limit: int = 3
    
    # === Stripe設定 ===
    premium_price_id: str = os.getenv("STRIPE_PREMIUM_PRICE_ID", "")
    
    class Config:
        env_prefix = "BILLING_"


# グローバル設定インスタンス
settings = Settings()
citer_settings = CiterSettings()
writer_settings = WriterSettings()
todos_settings = TodosSettings()
billing_settings = BillingSettings()
