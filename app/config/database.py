"""
データベース設定
SQLAlchemy設定とセッション管理
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from app.config.settings import settings


# === 同期データベース設定 ===
# PostgreSQLの場合はpsycopg2を使用、SQLiteの場合はそのまま
sync_database_url = settings.database_url
if settings.database_url.startswith("postgresql+asyncpg"):
    sync_database_url = settings.database_url.replace("postgresql+asyncpg://", "postgresql://")

try:
    engine = create_engine(
        sync_database_url,
        connect_args={"check_same_thread": False} if "sqlite" in sync_database_url else {}
    )
except Exception as e:
    # PostgreSQLドライバーが利用できない場合はSQLiteにフォールバック
    print(f"Warning: Could not create sync engine with {sync_database_url}, falling back to SQLite")
    sync_database_url = "sqlite:///./db.sqlite3"
    engine = create_engine(
        sync_database_url,
        connect_args={"check_same_thread": False}
    )

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# === 非同期データベース設定 ===
# SQLiteの場合はaiosqliteを使用
async_database_url = settings.database_url
if settings.database_url.startswith("sqlite"):
    async_database_url = settings.database_url.replace("sqlite://", "sqlite+aiosqlite://")
elif settings.database_url.startswith("postgresql"):
    # PostgreSQLの場合はasyncpgを使用
    async_database_url = settings.database_url.replace("postgresql://", "postgresql+asyncpg://")

try:
    async_engine = create_async_engine(async_database_url)
except Exception as e:
    # 非同期ドライバーが利用できない場合はSQLiteにフォールバック
    print(f"Warning: Could not create async engine with {async_database_url}, falling back to SQLite")
    async_database_url = "sqlite+aiosqlite:///./db.sqlite3"
    async_engine = create_async_engine(async_database_url)
AsyncSessionLocal = async_sessionmaker(
    async_engine, 
    class_=AsyncSession, 
    expire_on_commit=False
)

# === ベースクラス ===
Base = declarative_base()


# === 依存関係 ===
def get_db():
    """同期データベースセッション取得"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db():
    """非同期データベースセッション取得"""
    async with AsyncSessionLocal() as session:
        yield session


# === データベース初期化 ===
def create_tables():
    """テーブル作成"""
    Base.metadata.create_all(bind=engine)


async def create_tables_async():
    """非同期テーブル作成"""
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
