"""
データベース設定
SQLAlchemy設定とセッション管理
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from app.config.settings import settings


# === 同期データベース設定 ===
engine = create_engine(
    settings.database_url,
    connect_args={"check_same_thread": False} if "sqlite" in settings.database_url else {}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# === 非同期データベース設定 ===
# SQLiteの場合はaiosqliteを使用
async_database_url = settings.database_url
if settings.database_url.startswith("sqlite"):
    async_database_url = settings.database_url.replace("sqlite://", "sqlite+aiosqlite://")

async_engine = create_async_engine(async_database_url)
AsyncSessionLocal = async_sessionmaker(
    async_engine, 
    class_=AsyncSession, 
    expire_on_commit=False
)

# === ベースクラス ===
Base = declarative_base()


# === 依存関係 ===
def get_db():
    """同期データベースセッション取得"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db():
    """非同期データベースセッション取得"""
    async with AsyncSessionLocal() as session:
        yield session


# === データベース初期化 ===
def create_tables():
    """テーブル作成"""
    Base.metadata.create_all(bind=engine)


async def create_tables_async():
    """非同期テーブル作成"""
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
