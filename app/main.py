"""
新しいメインアプリケーション
機能別に整理されたモジュール構造
"""
import os
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.sessions import SessionMiddleware

from app.config.settings import settings
from app.config.database import create_tables_async
from app.shared.utils.logging import setup_logging
from app.api.routes import auth, citer2, writer

# ログ設定
setup_logging()

# FastAPIアプリケーション初期化
app = FastAPI(
    title=settings.app_name,
    debug=settings.debug,
    version="2.0.0"
)

# 静的ファイル
app.mount("/static", StaticFiles(directory="static"), name="static")

# セッションミドルウェア
app.add_middleware(SessionMiddleware, secret_key=settings.session_secret)

# APIルーター
app.include_router(auth.router)
app.include_router(citer2.router, prefix="/api")
app.include_router(writer.router, prefix="/api")

# TODOとBillingのルーターは後で追加
# app.include_router(todos.router, prefix="/api")
# app.include_router(billing.router, prefix="/api")


@app.on_event("startup")
async def startup_event():
    """アプリケーション起動時の処理"""
    # データベーステーブル作成
    await create_tables_async()


@app.get("/")
async def root():
    """ルートエンドポイント"""
    return {"message": "CITER and other AI Agents v2.0", "status": "running"}


@app.get("/health")
async def health_check():
    """ヘルスチェック"""
    return {"status": "healthy", "version": "2.0.0"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )
