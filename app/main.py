"""
新しいメインアプリケーション
機能別に整理されたモジュール構造
"""
import os
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from starlette.middleware.sessions import SessionMiddleware
import os

from app.config.settings import settings
from app.config.database import create_tables_async
from app.shared.utils.logging import setup_logging
from app.api.routes import auth, citer2, writer

# ログ設定
setup_logging()

# FastAPIアプリケーション初期化
app = FastAPI(
    title=settings.app_name,
    debug=settings.debug,
    version="2.0.0"
)

# 静的ファイル
app.mount("/static", StaticFiles(directory="static"), name="static")

# HTMLファイル用のルート
@app.get("/{filename}")
async def serve_html_files(filename: str):
    """HTMLファイルを提供"""
    if filename.endswith('.html'):
        file_path = f"static/{filename}"
        if os.path.exists(file_path):
            return FileResponse(file_path)
    # ファイルが見つからない場合は404を返す
    from fastapi import HTTPException
    raise HTTPException(status_code=404, detail="File not found")

# セッションミドルウェア
app.add_middleware(SessionMiddleware, secret_key=settings.session_secret)

# APIルーター
app.include_router(auth.router)
app.include_router(citer2.router, prefix="/api")
app.include_router(writer.router, prefix="/api")

# TODOとBillingのルーターは後で追加
# app.include_router(todos.router, prefix="/api")
# app.include_router(billing.router, prefix="/api")


@app.on_event("startup")
async def startup_event():
    """アプリケーション起動時の処理"""
    # データベーステーブル作成
    await create_tables_async()


@app.get("/")
async def root():
    """ルートエンドポイント - index.htmlを提供"""
    if os.path.exists("static/index.html"):
        return FileResponse("static/index.html")
    return {"message": "CITER and other AI Agents v2.0", "status": "running"}


@app.get("/health")
async def health_check():
    """ヘルスチェック"""
    return {"status": "healthy", "version": "2.0.0"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )
