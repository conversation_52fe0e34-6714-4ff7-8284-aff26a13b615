"""
認証モジュール
"""
from .models import User, Subscription, UsageRecord, HiddenTodo
from .schemas import UserRead, UserCreate, UserUpdate, UserDB, UserMe
from .backends import fastapi_users, current_user, current_active_user, oauth
from .manager import get_user_manager
from .dependencies import role_required, check_usage_limit, record_usage

__all__ = [
    # Models
    "User", "Subscription", "UsageRecord", "HiddenTodo",
    # Schemas
    "UserRead", "UserCreate", "UserUpdate", "UserDB", "UserMe",
    # Backends
    "fastapi_users", "current_user", "current_active_user", "oauth",
    # Manager
    "get_user_manager",
    # Dependencies
    "role_required", "check_usage_limit", "record_usage",
]