"""
認証バックエンド設定
JWT認証とOAuth設定
"""
import uuid
from fastapi_users import FastAPIUsers
from fastapi_users.authentication import AuthenticationBackend, BearerTransport, JWTStrategy
from authlib.integrations.starlette_client import OAuth

from app.config.settings import settings
from app.shared.auth.models import User
from app.shared.auth.manager import get_user_manager


# === JWT認証設定 ===
bearer_transport = BearerTransport(tokenUrl="auth/jwt/login")


def get_jwt_strategy() -> JWTStrategy:
    """JWT戦略取得"""
    return JWTStrategy(
        secret=settings.secret_key, 
        lifetime_seconds=settings.jwt_lifetime_seconds
    )


jwt_backend = AuthenticationBackend(
    name="jwt",
    transport=bearer_transport,
    get_strategy=get_jwt_strategy,
)


# === FastAPI Users設定 ===
fastapi_users = FastAPIUsers[User, uuid.UUID](get_user_manager, [jwt_backend])

current_user = fastapi_users.current_user()
current_active_user = fastapi_users.current_user(active=True)


# === OAuth設定 ===
oauth = OAuth()
oauth.register(
    name="google",
    client_id=settings.google_client_id,
    client_secret=settings.google_client_secret,
    server_metadata_url="https://accounts.google.com/.well-known/openid-configuration",
    client_kwargs={},
)


# === OAuth スコープ ===
BASE_SCOPES = ["openid", "email", "profile"]
GMAIL_SCOPES = BASE_SCOPES + ["https://www.googleapis.com/auth/gmail.readonly"]
CALENDAR_SCOPES = BASE_SCOPES + [
    "https://www.googleapis.com/auth/calendar.readonly",
    "https://www.googleapis.com/auth/calendar.events.readonly",
]
