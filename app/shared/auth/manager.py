"""
ユーザー管理とFastAPI-Users設定
"""
import uuid
import logging
from typing import Optional

from fastapi import Depends
from fastapi_users import BaseUserManager
from fastapi_users.db import SQLAlchemyUserDatabase
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.settings import settings
from app.config.database import get_async_db
from app.shared.auth.models import User
from app.shared.auth.schemas import UserCreate

logger = logging.getLogger(__name__)


class UserManager(BaseUserManager[User, uuid.UUID]):
    """ユーザー管理クラス"""
    reset_password_token_secret = settings.secret_key
    verification_token_secret = settings.secret_key

    def parse_id(self, value: str) -> uuid.UUID:
        return uuid.UUID(value)

    async def on_after_register(self, user: User, request: Optional[any] = None):
        """ユーザー登録後の処理"""
        logger.info(f"User {user.id} has registered.")

    async def on_after_login(self, user: User, request: Optional[any] = None):
        """ログイン後の処理"""
        logger.info(f"User {user.id} has logged in.")

    async def on_after_forgot_password(self, user: User, token: str, request: Optional[any] = None):
        """パスワードリセット要求後の処理"""
        logger.info(f"User {user.id} has forgot their password. Reset token: {token}")


async def get_user_db(session: AsyncSession = Depends(get_async_db)):
    """ユーザーデータベース取得"""
    yield SQLAlchemyUserDatabase(session, User)


async def get_user_manager(user_db=Depends(get_user_db)):
    """ユーザーマネージャー取得"""
    yield UserManager(user_db)
