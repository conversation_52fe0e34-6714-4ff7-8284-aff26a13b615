"""
認証関連のスキーマ
FastAPI-Users用のスキーマ定義
"""
import uuid
from typing import Optional, Dict, Any
from fastapi_users import schemas
from pydantic import BaseModel


class UserRead(schemas.BaseUser[uuid.UUID]):
    """ユーザー読み取り用スキーマ"""
    role: int
    gmail_tokens: Optional[Dict[str, Any]] = None
    calendar_tokens: Optional[Dict[str, Any]] = None
    stripe_customer_id: Optional[str] = None


class UserCreate(schemas.BaseUserCreate):
    """ユーザー作成用スキーマ"""
    role: int = 0


class UserUpdate(schemas.BaseUserUpdate):
    """ユーザー更新用スキーマ"""
    role: Optional[int] = None
    gmail_tokens: Optional[Dict[str, Any]] = None
    calendar_tokens: Optional[Dict[str, Any]] = None
    stripe_customer_id: Optional[str] = None


class UserDB(UserRead):
    """データベース用ユーザースキーマ（後方互換性のため）"""
    pass


class UserMe(UserRead):
    """現在のユーザー情報用スキーマ（後方互換性のため）"""
    pass


class SubscriptionSchema(BaseModel):
    """サブスクリプション情報スキーマ"""
    id: str
    user_id: uuid.UUID
    status: str
    current_period_end: str
    
    class Config:
        from_attributes = True


class UsageRecordSchema(BaseModel):
    """使用記録スキーマ"""
    id: uuid.UUID
    user_id: uuid.UUID
    feature: str
    action: str
    timestamp: str
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True
