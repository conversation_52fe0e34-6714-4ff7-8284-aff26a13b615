"""
認証関連の依存関係
ロール制限、使用制限チェックなど
"""
import datetime as dt
from typing import Optional
from fastapi import Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from app.config.database import get_async_db
from app.config.settings import billing_settings
from app.shared.auth.backends import current_active_user
from app.shared.auth.models import User, UsageRecord
from app.shared.auth.schemas import UserDB


def role_required(min_role: int):
    """
    指定されたロール以上のユーザーのみアクセス可能
    不足している場合はアップグレード案内を返す
    """
    async def dependency(user: User = Depends(current_active_user)):
        if user.role < min_role:
            return JSONResponse(
                status_code=200,
                content={
                    "plan_required": True,
                    "message": "この機能を使用するにはプレミアムプランが必要です。",
                    "upgrade_url": "/billing.html"
                }
            )
        return user
    return dependency


async def check_usage_limit(
    user_id: str, 
    feature: str = "general",
    session: AsyncSession = Depends(get_async_db)
) -> bool:
    """
    使用制限チェック
    無料ユーザーの日次制限を確認
    """
    today = dt.date.today()
    today_start = dt.datetime.combine(today, dt.time.min)
    
    # 今日の使用回数を取得
    stmt = select(func.count(UsageRecord.id)).where(
        UsageRecord.user_id == user_id,
        UsageRecord.feature == feature,
        UsageRecord.timestamp >= today_start
    )
    result = await session.execute(stmt)
    usage_count = result.scalar() or 0
    
    return usage_count < billing_settings.free_daily_limit


async def record_usage(
    user_id: str,
    feature: str,
    action: str,
    metadata: Optional[dict] = None,
    session: AsyncSession = Depends(get_async_db)
):
    """使用記録を保存"""
    usage_record = UsageRecord(
        user_id=user_id,
        feature=feature,
        action=action,
        metadata=metadata
    )
    session.add(usage_record)
    await session.commit()


class UsageLimitChecker:
    """使用制限チェッカー"""
    
    def __init__(self, feature: str):
        self.feature = feature
    
    async def __call__(
        self,
        user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_async_db)
    ) -> User:
        """使用制限をチェックし、制限に達している場合は例外を発生"""
        # 有料ユーザーは制限なし
        if user.role >= 2:
            return user
        
        # 無料ユーザーの制限チェック
        if not await check_usage_limit(str(user.id), self.feature, session):
            raise HTTPException(
                status_code=429,
                detail={
                    "limit_exceeded": True,
                    "message": f"1日の使用回数制限（{billing_settings.free_daily_limit}回）に達しました。プレミアムプランにアップグレードすると無制限に使用できます。",
                    "upgrade_url": "/billing.html"
                }
            )
        
        return user


# 機能別の使用制限チェッカー
citer_usage_limit = UsageLimitChecker("citer2")
writer_usage_limit = UsageLimitChecker("writer")
todos_usage_limit = UsageLimitChecker("todos")
