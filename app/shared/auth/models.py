"""
認証関連のデータモデル
ユーザー、サブスクリプション、使用制限など
"""
import datetime as dt
import uuid
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSON
from fastapi_users.db import SQLAlchemyBaseUserTableUUID

from app.config.database import Base


class User(SQLAlchemyBaseUserTableUUID, Base):
    """ユーザーモデル"""
    __tablename__ = "user"

    # FastAPI-Usersの基本フィールドに加えて独自フィールドを追加
    role = Column(Integer, default=0)  # 0: guest, 1: free, 2: paid
    gmail_tokens = Column(JSON, nullable=True)  # OAuth2 token info
    calendar_tokens = Column(JSON, nullable=True)
    stripe_customer_id = Column(String, unique=True, nullable=True)
    
    # リレーション
    hidden_todos = relationship("HiddenTodo", back_populates="user", cascade="all, delete-orphan")
    subscriptions = relationship("Subscription", back_populates="user")
    usage_records = relationship("UsageRecord", back_populates="user")


class Subscription(Base):
    """サブスクリプション情報"""
    __tablename__ = "subscription"
    
    id = Column(String, primary_key=True)  # Stripe subscription id
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    status = Column(String, nullable=False)  # active / past_due / canceled
    current_period_end = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=dt.datetime.utcnow, nullable=False)
    
    # リレーション
    user = relationship("User", back_populates="subscriptions")


class UsageRecord(Base):
    """使用記録"""
    __tablename__ = "usage_records"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    feature = Column(String, nullable=False)  # citer2, writer, todos
    action = Column(String, nullable=False)   # search, generate, ocr
    timestamp = Column(DateTime, default=dt.datetime.utcnow, nullable=False)
    metadata = Column(JSON, nullable=True)    # 追加情報
    
    # リレーション
    user = relationship("User", back_populates="usage_records")


class HiddenTodo(Base):
    """非表示ToDo"""
    __tablename__ = "hidden_todos"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    task_id = Column(UUID(as_uuid=True), nullable=False)
    hidden_at = Column(DateTime, default=dt.datetime.utcnow, nullable=False)
    
    # リレーション
    user = relationship("User", back_populates="hidden_todos")
