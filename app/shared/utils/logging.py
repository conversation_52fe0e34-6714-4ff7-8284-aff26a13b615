"""
ログ設定ユーティリティ
"""
import logging
import sys
from app.config.settings import settings


def setup_logging():
    """ログ設定を初期化"""
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # デバッグモードの場合は詳細ログを有効化
    if settings.debug:
        logging.getLogger("sqlalchemy.engine").setLevel(logging.INFO)


def get_logger(name: str) -> logging.Logger:
    """ロガー取得"""
    return logging.getLogger(name)
