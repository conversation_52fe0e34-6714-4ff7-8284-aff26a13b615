"""
カスタム例外クラス
"""
from typing import Optional, Dict, Any


class AppException(Exception):
    """アプリケーション基底例外"""
    def __init__(self, message: str, code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.code = code
        self.details = details or {}
        super().__init__(self.message)


class AuthenticationError(AppException):
    """認証エラー"""
    pass


class AuthorizationError(AppException):
    """認可エラー"""
    pass


class UsageLimitError(AppException):
    """使用制限エラー"""
    pass


class ExternalServiceError(AppException):
    """外部サービスエラー"""
    pass


class ValidationError(AppException):
    """バリデーションエラー"""
    pass


class NotFoundError(AppException):
    """リソース未発見エラー"""
    pass
