"""
Writer機能のスキーマ
OCR、論文生成API用のデータ構造定義
"""
import uuid
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class OcrRequest(BaseModel):
    """OCRリクエスト"""
    method: str = Field("azure", description="OCR方法")
    output_format: str = Field("layout", description="出力形式")
    preprocess: bool = Field(True, description="前処理を行うか")


class OcrResponse(BaseModel):
    """OCRレスポンス"""
    job_id: str = Field(..., description="ジョブID")
    status: str = Field(..., description="処理ステータス")
    file_count: int = Field(..., description="処理ファイル数")
    message: str = Field("", description="メッセージ")


class OcrResult(BaseModel):
    """OCR結果"""
    file_index: int
    filename: Optional[str] = None
    method: str
    text: str
    confidence: Optional[float] = None
    bounding_boxes: Optional[List[Dict[str, Any]]] = None
    success: bool = True
    error: Optional[str] = None


class OcrJobStatus(BaseModel):
    """OCRジョブステータス"""
    job_id: str
    status: str
    file_count: int
    completed_count: int = 0
    results: List[OcrResult] = []
    error: Optional[str] = None


class PaperGenerationRequest(BaseModel):
    """論文生成リクエスト"""
    paper_id: str = Field(..., description="論文ID")
    note_text: str = Field(..., description="実験ノートテキスト")
    model: str = Field("o3", description="使用するモデル")
    language: str = Field("ja", description="言語")
    sections: Optional[List[str]] = Field(None, description="生成するセクション")


class PaperRewriteRequest(BaseModel):
    """論文リライトリクエスト"""
    paper_id: str = Field(..., description="論文ID")
    paper_text: str = Field(..., description="論文テキスト")
    rewrite_type: str = Field("improve", description="リライトタイプ")
    model: str = Field("o3", description="使用するモデル")
    target_sections: Optional[List[str]] = Field(None, description="対象セクション")


class CitationAddRequest(BaseModel):
    """文献追加リクエスト"""
    paper_id: str = Field(..., description="論文ID")
    paper_text: str = Field(..., description="論文テキスト")
    intro_text: Optional[str] = Field(None, description="イントロダクションテキスト")
    discussion_text: Optional[str] = Field(None, description="考察テキスト")
    hits: int = Field(10, ge=1, le=50, description="検索する論文数")
    threshold: int = Field(4, ge=1, le=10, description="引用候補とする最低スコア")
    max_refs: int = Field(3, ge=1, le=10, description="1文あたりの最大引用数")
    model: str = Field("o3", description="使用するモデル")
    author_last_name: Optional[str] = Field(None, description="著者姓")
    author_first_name: Optional[str] = Field(None, description="著者名")
    prioritize_author: bool = Field(False, description="著者の論文を優先するか")
    require_high_impact: bool = Field(False, description="高インパクトファクター論文のみ")


class PaperResponse(BaseModel):
    """論文レスポンス"""
    paper_id: str
    status: str
    message: str = ""
    result: Optional[Dict[str, Any]] = None


class PaperContent(BaseModel):
    """論文コンテンツ"""
    paper_id: str
    title: Optional[str] = None
    content: str
    sections: Optional[Dict[str, str]] = None
    citations: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None
    version: int = 1
    status: str = "draft"
    
    class Config:
        from_attributes = True


class WriterPreferencesSchema(BaseModel):
    """Writer設定スキーマ"""
    default_ocr_method: str = Field("azure")
    ocr_preprocess: bool = Field(True)
    default_model: str = Field("o3")
    auto_citation: bool = Field(False)
    preferred_citation_style: str = Field("apa")
    language: str = Field("ja")
    
    class Config:
        from_attributes = True


class SectionIdentificationRequest(BaseModel):
    """セクション識別リクエスト"""
    text: str = Field(..., description="識別対象のテキスト")
    format: str = Field("json", description="出力形式")


class SectionIdentificationResponse(BaseModel):
    """セクション識別レスポンス"""
    sections: Dict[str, str] = Field(..., description="識別されたセクション")
    format: str = Field("json", description="出力形式")
