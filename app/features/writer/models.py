"""
Writer機能のデータモデル
OCR、論文生成関連のモデル
"""
import datetime as dt
import uuid
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Boolean, Text, Float
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSON

from app.shared.models.base import BaseModel


class OcrJob(BaseModel):
    """OCR処理ジョブ"""
    __tablename__ = "writer_ocr_jobs"
    
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    job_id = Column(String, unique=True, nullable=False, index=True)
    status = Column(String, default="pending", nullable=False)  # pending, processing, completed, failed
    file_count = Column(Integer, default=0, nullable=False)
    method = Column(String, default="azure", nullable=False)  # azure, google, openai
    result = Column(JSON, nullable=True)  # OCR結果
    error_message = Column(Text, nullable=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)


class Paper(BaseModel):
    """論文データ"""
    __tablename__ = "writer_papers"
    
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    paper_id = Column(String, unique=True, nullable=False, index=True)
    title = Column(String, nullable=True)
    content = Column(Text, nullable=False)
    sections = Column(JSON, nullable=True)  # セクション構造
    citations = Column(JSON, nullable=True)  # 引用情報
    metadata = Column(JSON, nullable=True)  # メタデータ
    version = Column(Integer, default=1, nullable=False)
    status = Column(String, default="draft", nullable=False)  # draft, published, archived


class PaperGeneration(BaseModel):
    """論文生成ジョブ"""
    __tablename__ = "writer_paper_generations"
    
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    paper_id = Column(String, nullable=False, index=True)
    job_type = Column(String, nullable=False)  # generate, rewrite, add_citations
    input_data = Column(JSON, nullable=False)  # 入力データ
    parameters = Column(JSON, nullable=True)  # 生成パラメータ
    result = Column(JSON, nullable=True)  # 生成結果
    status = Column(String, default="pending", nullable=False)
    error_message = Column(Text, nullable=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)


class OcrResult(BaseModel):
    """OCR結果"""
    __tablename__ = "writer_ocr_results"
    
    job_id = Column(UUID(as_uuid=True), ForeignKey("writer_ocr_jobs.id"), nullable=False)
    file_index = Column(Integer, nullable=False)
    filename = Column(String, nullable=True)
    method = Column(String, nullable=False)
    text = Column(Text, nullable=False)
    confidence = Column(Float, nullable=True)
    bounding_boxes = Column(JSON, nullable=True)  # バウンディングボックス情報
    metadata = Column(JSON, nullable=True)
    
    # リレーション
    job = relationship("OcrJob", backref="results")


class WriterPreferences(BaseModel):
    """Writer設定"""
    __tablename__ = "writer_user_preferences"
    
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False, unique=True)
    default_ocr_method = Column(String, default="azure", nullable=False)
    ocr_preprocess = Column(Boolean, default=True, nullable=False)
    default_model = Column(String, default="o3", nullable=False)
    auto_citation = Column(Boolean, default=False, nullable=False)
    preferred_citation_style = Column(String, default="apa", nullable=False)
    language = Column(String, default="ja", nullable=False)  # ja, en
