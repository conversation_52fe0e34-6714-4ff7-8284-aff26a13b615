"""
Writer機能のOCRサービス
Azure Computer Vision API専用
"""
import os
import json
import re
import io
import time
import logging
from typing import Dict, List, Any, Optional
import requests
from PIL import Image, ImageEnhance, ImageOps
import numpy as np

from app.config.settings import settings, writer_settings
from app.shared.utils.logging import get_logger

logger = get_logger(__name__)


class OCRService:
    """OCRサービスクラス"""
    
    def __init__(self):
        self.azure_available = False
        
        # Azure Computer Vision APIの初期化
        try:
            self.azure_endpoint = settings.azure_cv_endpoint
            self.azure_key = settings.azure_cv_key
            
            if self.azure_endpoint and self.azure_key:
                self.azure_available = True
                logger.info("Azure Computer Vision API initialized successfully")
            else:
                logger.error("Azure Computer Vision API credentials not found")
                raise Exception("Azure Computer Vision API credentials are required")
        except Exception as e:
            logger.error(f"Azure Computer Vision API initialization failed: {e}")
            raise
    
    def preprocess_image_for_ocr(self, image_data: bytes) -> bytes:
        """
        MacのPhotoアプリの調整を再現して画像を前処理
        Brilliance, Exposure, Brightness, Contrastを最大化
        """
        try:
            # バイトデータからPIL Imageを作成
            image = Image.open(io.BytesIO(image_data))
            
            # RGBAの場合はRGBに変換
            if image.mode == 'RGBA':
                # 白背景で合成
                background = Image.new('RGB', image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[-1])
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 1. Brightness（明度）調整 - 1.5倍
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(1.5)
            
            # 2. Contrast（コントラスト）調整 - 2.0倍
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)
            
            # 3. Exposure（露出）調整の近似 - ガンマ補正で実現
            # 露出を上げる効果をガンマ値0.7で近似
            gamma = 0.7
            gamma_table = [int(((i / 255.0) ** gamma) * 255) for i in range(256)]
            image = image.point(gamma_table * 3)  # RGB各チャンネルに適用
            
            # 4. Brilliance（輝度）調整の近似
            # ハイライトを強調し、シャドウを持ち上げる
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(1.2)
            
            # 5. シャープネス調整（オプション）
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.3)
            
            # バイトデータに変換して返す
            output_buffer = io.BytesIO()
            image.save(output_buffer, format='JPEG', quality=95)
            return output_buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Image preprocessing failed: {e}")
            # 前処理に失敗した場合は元の画像データを返す
            return image_data
    
    def extract_text_azure(
        self, 
        image_data: bytes, 
        output_format: str = "structured", 
        preprocess: bool = True
    ) -> Dict[str, Any]:
        """
        Azure Computer Vision APIを使用してOCR処理（手書き特化）
        """
        if not self.azure_available:
            raise Exception("Azure Computer Vision API is not available")
        
        try:
            # 画像前処理を実行
            if preprocess:
                logger.info("🖼️ 画像前処理を実行中...")
                image_data = self.preprocess_image_for_ocr(image_data)
            
            # Read APIを使用（手書き文字に最適）
            read_url = f"{self.azure_endpoint}/vision/v3.2/read/analyze"
            
            headers = {
                'Ocp-Apim-Subscription-Key': self.azure_key,
                'Content-Type': 'application/octet-stream'
            }
            
            params = {
                'language': 'ja',  # 日本語を指定
                'readingOrder': 'natural'  # 自然な読み順
            }
            
            # 初回リクエスト
            response = requests.post(read_url, headers=headers, params=params, data=image_data)
            response.raise_for_status()
            
            # Operation-Locationヘッダーから結果取得URLを取得
            operation_location = response.headers.get('Operation-Location')
            if not operation_location:
                raise Exception("Operation-Location header not found")
            
            # 結果を取得（ポーリング）
            max_attempts = 30
            for attempt in range(max_attempts):
                time.sleep(1)
                result_response = requests.get(operation_location, headers=headers)
                result_response.raise_for_status()
                result = result_response.json()
                
                if result['status'] == 'succeeded':
                    break
                elif result['status'] == 'failed':
                    raise Exception(f"OCR processing failed: {result.get('message', 'Unknown error')}")
                
                if attempt == max_attempts - 1:
                    raise Exception("OCR processing timeout")
            
            # 出力形式に応じて処理
            if output_format == "structured":
                return self._process_azure_structured(result)
            elif output_format == "layout":
                return self._process_azure_layout(result)
            else:
                return self._process_azure_text(result)
                
        except Exception as e:
            logger.error(f"Azure OCR failed: {e}")
            return {
                "success": False,
                "text": "",
                "confidence": 0,
                "method": "azure_computer_vision",
                "error": str(e)
            }
    
    def _process_azure_text(self, result: Dict) -> Dict[str, Any]:
        """Azure OCR結果をテキスト形式で処理"""
        try:
            text_lines = []
            total_confidence = 0
            line_count = 0
            
            for page in result.get('analyzeResult', {}).get('readResults', []):
                for line in page.get('lines', []):
                    text_lines.append(line['text'])
                    if 'confidence' in line:
                        total_confidence += line['confidence']
                        line_count += 1
            
            avg_confidence = total_confidence / line_count if line_count > 0 else 0
            
            return {
                "success": True,
                "text": "\n".join(text_lines),
                "confidence": avg_confidence,
                "method": "azure_computer_vision"
            }
            
        except Exception as e:
            logger.error(f"Azure text processing failed: {e}")
            return {
                "success": False,
                "text": "",
                "confidence": 0,
                "method": "azure_computer_vision",
                "error": str(e)
            }
    
    def _process_azure_structured(self, result: Dict) -> Dict[str, Any]:
        """Azure OCR結果を構造化形式で処理"""
        try:
            structured_data = {
                "pages": [],
                "tables": [],
                "text_blocks": []
            }
            
            for page_idx, page in enumerate(result.get('analyzeResult', {}).get('readResults', [])):
                page_data = {
                    "page_number": page_idx + 1,
                    "lines": [],
                    "words": []
                }
                
                for line in page.get('lines', []):
                    line_data = {
                        "text": line['text'],
                        "bounding_box": line.get('boundingBox', []),
                        "confidence": line.get('confidence', 0)
                    }
                    page_data["lines"].append(line_data)
                    
                    # 単語レベルの情報も追加
                    for word in line.get('words', []):
                        word_data = {
                            "text": word['text'],
                            "bounding_box": word.get('boundingBox', []),
                            "confidence": word.get('confidence', 0)
                        }
                        page_data["words"].append(word_data)
                
                structured_data["pages"].append(page_data)
            
            # テキスト全体を結合
            all_text = "\n".join([
                line['text'] for page in structured_data["pages"] 
                for line in page["lines"]
            ])
            
            return {
                "success": True,
                "text": all_text,
                "structured_data": structured_data,
                "method": "azure_computer_vision"
            }
            
        except Exception as e:
            logger.error(f"Azure structured processing failed: {e}")
            return {
                "success": False,
                "text": "",
                "confidence": 0,
                "method": "azure_computer_vision",
                "error": str(e)
            }
    
    def _process_azure_layout(self, result: Dict) -> Dict[str, Any]:
        """Azure OCR結果をレイアウト保持形式で処理"""
        # 簡略化実装 - 実際は既存のocr_service.pyから移行
        return self._process_azure_structured(result)
    
    def get_available_methods(self) -> List[str]:
        """利用可能なOCR方法を返す"""
        methods = []
        if self.azure_available:
            methods.extend(["azure", "azure_structured", "azure_layout"])
        return methods
    
    def extract_text(
        self, 
        image_data: bytes, 
        method: str = "auto", 
        output_format: str = "layout"
    ) -> Dict[str, Any]:
        """
        OCR処理のメインエントリーポイント
        """
        if not self.azure_available:
            return {
                "success": False,
                "text": "",
                "confidence": 0,
                "method": "none",
                "error": "Azure Computer Vision API is not available"
            }
        
        if method in ["auto", "azure", "azure_layout"]:
            return self.extract_text_azure(image_data, output_format)
        else:
            return {
                "success": False,
                "text": "",
                "confidence": 0,
                "method": method,
                "error": f"Unsupported OCR method: {method}. Only Azure is supported."
            }


# サービスインスタンス
ocr_service = OCRService()
