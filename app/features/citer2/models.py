"""
CITER2機能のデータモデル
文献検索、引用管理関連のモデル
"""
import datetime as dt
import uuid
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Boolean, Text, Float
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSON

from app.shared.models.base import BaseModel


class SearchJob(BaseModel):
    """文献検索ジョブ"""
    __tablename__ = "citer2_search_jobs"
    
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    job_id = Column(String, unique=True, nullable=False, index=True)
    status = Column(String, default="pending", nullable=False)  # pending, running, completed, failed
    input_text = Column(Text, nullable=False)
    parameters = Column(JSON, nullable=True)  # 検索パラメータ
    result = Column(JSON, nullable=True)  # 検索結果
    error_message = Column(Text, nullable=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)


class Citation(BaseModel):
    """引用情報"""
    __tablename__ = "citer2_citations"
    
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    pmid = Column(String, nullable=False, index=True)
    title = Column(Text, nullable=False)
    authors = Column(Text, nullable=True)
    journal = Column(String, nullable=True)
    year = Column(Integer, nullable=True)
    doi = Column(String, nullable=True)
    abstract = Column(Text, nullable=True)
    impact_factor = Column(Float, nullable=True)
    citation_count = Column(Integer, nullable=True)
    metadata = Column(JSON, nullable=True)  # 追加のメタデータ


class SearchResult(BaseModel):
    """検索結果"""
    __tablename__ = "citer2_search_results"
    
    job_id = Column(UUID(as_uuid=True), ForeignKey("citer2_search_jobs.id"), nullable=False)
    sentence_index = Column(Integer, nullable=False)
    sentence_text = Column(Text, nullable=False)
    keyword = Column(String, nullable=True)
    query = Column(String, nullable=True)
    pmid = Column(String, nullable=False)
    score = Column(Float, nullable=False)
    reason = Column(Text, nullable=True)
    accepted = Column(Boolean, default=False, nullable=False)
    
    # リレーション
    job = relationship("SearchJob", backref="results")


class UserPreferences(BaseModel):
    """ユーザー設定"""
    __tablename__ = "citer2_user_preferences"
    
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False, unique=True)
    default_hits = Column(Integer, default=10, nullable=False)
    default_threshold = Column(Integer, default=4, nullable=False)
    default_max_refs = Column(Integer, default=3, nullable=False)
    default_model = Column(String, default="o3", nullable=False)
    require_high_impact = Column(Boolean, default=False, nullable=False)
    preferred_journals = Column(JSON, nullable=True)  # 優先ジャーナルリスト
    excluded_journals = Column(JSON, nullable=True)   # 除外ジャーナルリスト
