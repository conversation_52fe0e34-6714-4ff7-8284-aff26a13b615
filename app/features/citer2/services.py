"""
CITER2機能のサービス層
文献検索、引用生成のビジネスロジック
"""
import os
import json
import uuid
import logging
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed

import openai
from Bio import Entrez
from langchain_openai import Chat<PERSON>penAI

from app.config.settings import settings, citer_settings
from app.shared.utils.logging import get_logger
from app.features.citer2.schemas import SearchRequest, SearchResultResponse
# パイプライン関数は必要に応じて動的インポート

logger = get_logger(__name__)


class CiterService:
    """CITER2サービスクラス"""
    
    def __init__(self):
        # Entrez設定
        Entrez.email = settings.ncbi_email or "<EMAIL>"
        Entrez.api_key = settings.ncbi_api_key
        
        # OpenAI設定
        if not settings.openai_api_key:
            raise RuntimeError("OPENAI_API_KEY not set")
        
        self.openai_client = openai.OpenAI(api_key=settings.openai_api_key)
        self.llm_small = ChatOpenAI(
            openai_api_key=settings.openai_api_key, 
            model_name="gpt-4.1-mini"
        )
    
    async def search_literature(
        self, 
        request: SearchRequest, 
        progress_callback: Optional[Callable] = None
    ) -> SearchResultResponse:
        """文献検索メイン処理"""
        try:
            # 進捗通知
            if progress_callback:
                progress_callback(json.dumps({
                    "status": "starting",
                    "message": "文献検索を開始しています..."
                }, ensure_ascii=False))
            
            # 1. 文分割
            from .pipeline import split_sentences
            sentences = split_sentences(request.text)
            if progress_callback:
                progress_callback(json.dumps({
                    "status": "processing",
                    "step": "sentence_split",
                    "message": f"{len(sentences)}個の文に分割しました",
                    "sentences_count": len(sentences)
                }, ensure_ascii=False))
            
            # 2. 著者の論文を優先検索（オプション）
            author_papers = []
            if request.prioritize_author and request.author_last_name:
                if progress_callback:
                    progress_callback(json.dumps({
                        "status": "processing",
                        "step": "author_search",
                        "message": f"著者 {request.author_last_name} の論文を検索中..."
                    }, ensure_ascii=False))
                
                author_papers = await self._search_author_papers(
                    request.author_last_name,
                    request.author_first_name,
                    progress_callback
                )
            
            # 3. 各文に対して文献検索
            results = []
            total_sentences = len(sentences)
            
            with ThreadPoolExecutor(max_workers=citer_settings.max_workers) as executor:
                future_to_idx = {}
                
                for idx, sentence in enumerate(sentences):
                    if progress_callback:
                        progress_callback(json.dumps({
                            "status": "processing",
                            "step": "literature_search",
                            "message": f"文 {idx + 1}/{total_sentences} の文献検索中...",
                            "progress": (idx / total_sentences) * 100
                        }, ensure_ascii=False))
                    
                    # キーワード抽出
                    from .pipeline import extract_keywords
                    keywords = extract_keywords(sentence, request.model)
                    
                    # 文献検索
                    future = executor.submit(
                        self._search_sentence_literature,
                        sentence,
                        keywords,
                        request,
                        author_papers
                    )
                    future_to_idx[future] = idx
                
                # 結果収集
                for future in as_completed(future_to_idx):
                    idx = future_to_idx[future]
                    try:
                        sentence_result = future.result()
                        results.append({
                            "sentence_index": idx,
                            "sentence_text": sentences[idx],
                            **sentence_result
                        })
                    except Exception as e:
                        logger.error(f"Error processing sentence {idx}: {e}")
                        results.append({
                            "sentence_index": idx,
                            "sentence_text": sentences[idx],
                            "citations": [],
                            "error": str(e)
                        })
            
            # 4. 引用付きテキスト生成
            cited_text = self._generate_cited_text(request.text, results)
            
            # 5. 重複除去
            from .pipeline import clean_duplicate_citations, generate_ris_content
            cited_text = clean_duplicate_citations(cited_text, request.model)

            # 6. RIS生成
            ris_content = generate_ris_content(results)
            
            if progress_callback:
                progress_callback(json.dumps({
                    "status": "completed",
                    "message": "文献検索が完了しました"
                }, ensure_ascii=False))
            
            return SearchResultResponse(
                job_id=str(uuid.uuid4()),
                status="completed",
                input_text=request.text,
                cited_text=cited_text,
                results=results,
                ris_url=f"/download/ris/{uuid.uuid4()}",
                statistics={
                    "total_sentences": len(sentences),
                    "total_citations": sum(len(r.get("citations", [])) for r in results),
                    "processing_time": "N/A"
                }
            )
            
        except Exception as e:
            logger.error(f"Literature search failed: {e}")
            if progress_callback:
                progress_callback(json.dumps({
                    "status": "failed",
                    "error": str(e)
                }, ensure_ascii=False))
            raise
    
    async def _search_author_papers(
        self, 
        last_name: str, 
        first_name: Optional[str] = None,
        progress_callback: Optional[Callable] = None
    ) -> List[Dict]:
        """著者の論文を検索"""
        # 実装は既存のpipeline.pyから移行
        # ここでは簡略化
        return []
    
    def _search_sentence_literature(
        self,
        sentence: str,
        keywords: List[str],
        request: SearchRequest,
        author_papers: List[Dict]
    ) -> Dict:
        """単一文の文献検索"""
        # 実装は既存のpipeline.pyから移行
        # ここでは簡略化
        return {
            "keyword": ", ".join(keywords),
            "query": " ".join(keywords),
            "citations": []
        }
    
    def _generate_cited_text(self, original_text: str, results: List[Dict]) -> str:
        """引用付きテキスト生成"""
        # 実装は既存のpipeline.pyから移行
        # ここでは簡略化
        return original_text


# サービスインスタンス
citer_service = CiterService()
