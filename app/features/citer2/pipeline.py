"""
CITER2パイプライン機能
既存のpipeline.pyから文献検索関連機能を移行
"""
import re
import json
import logging
from typing import List, Dict, Any, Optional, Callable

from app.config.settings import settings
from app.shared.utils.logging import get_logger

logger = get_logger(__name__)


def split_sentences(text: str) -> List[str]:
    """
    テキストを文単位に分割する関数（日本語対応版）
    段落構造を保持し、著者業績の便宜的番号を除去
    """
    # 段落区切りを保護
    text = re.sub(r'\n\s*\n', '__PARAGRAPH_BREAK__', text)
    
    # 著者業績の便宜的番号を除去（例：「1) 論文タイトル」→「論文タイトル」）
    text = re.sub(r'^\s*\d+\)\s*', '', text, flags=re.MULTILINE)
    
    # 日本語の文区切り文字で分割
    sentences = re.split(r'[。！？]', text)
    
    # 空文字列と短すぎる文を除去
    sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]
    
    # 段落区切りを復元
    sentences = [s.replace('__PARAGRAPH_BREAK__', '\n\n') for s in sentences]
    
    return sentences


def extract_keywords(sentence: str, model: str = "gpt-4.1-mini") -> List[str]:
    """
    文からキーワードを抽出
    """
    # 簡略化実装 - 実際は既存のpipeline.pyから移行
    # GPTを使用してキーワード抽出
    words = sentence.split()
    # 仮実装：名詞っぽい単語を抽出
    keywords = [w for w in words if len(w) > 2 and not w.isdigit()]
    return keywords[:5]  # 最大5個


def find_citable_references(
    sentence: str, 
    query: str, 
    hits: int = 10,
    progress_callback: Optional[Callable] = None
) -> List[Dict]:
    """
    文献検索を実行
    """
    # 簡略化実装 - 実際は既存のpipeline.pyから移行
    # PubMed検索、スコアリング、選定を実行
    return []


def clean_duplicate_citations(text: str, model: str = "gpt-4.1-mini") -> str:
    """
    重複引用番号の除去
    """
    # 簡略化実装 - 実際は既存のpipeline.pyから移行
    # 重複する引用番号を統合
    return text


def generate_ris_content(results: List[Dict]) -> str:
    """
    RIS形式の参考文献を生成
    """
    # 簡略化実装 - 実際は既存のpipeline.pyから移行
    ris_lines = []
    
    for result in results:
        citations = result.get("citations", [])
        for citation in citations:
            ris_lines.extend([
                "TY  - JOUR",
                f"TI  - {citation.get('title', '')}",
                f"AU  - {citation.get('authors', '')}",
                f"JO  - {citation.get('journal', '')}",
                f"PY  - {citation.get('year', '')}",
                f"DO  - {citation.get('doi', '')}",
                f"UR  - https://pubmed.ncbi.nlm.nih.gov/{citation.get('pmid', '')}",
                "ER  - ",
                ""
            ])
    
    return "\n".join(ris_lines)


def identify_citation_sentences(text: str, sentences: List[str]) -> List[int]:
    """
    引用が必要な文を特定
    """
    # 簡略化実装 - 実際は既存のpipeline.pyから移行
    # 背景知識や先行研究に関する文を特定
    citation_indices = []
    
    for idx, sentence in enumerate(sentences):
        # 引用が必要そうなキーワードを含む文を特定
        citation_keywords = [
            "報告", "研究", "発見", "明らか", "示唆", "知られている",
            "established", "reported", "shown", "demonstrated"
        ]
        
        if any(keyword in sentence for keyword in citation_keywords):
            citation_indices.append(idx)
    
    return citation_indices


def score_paper_relevance(sentence: str, paper_info: Dict, model: str = "gpt-4.1-mini") -> Dict:
    """
    論文の関連性をスコアリング
    """
    # 簡略化実装 - 実際は既存のpipeline.pyから移行
    # GPTを使用して関連性スコアと理由を生成
    return {
        "score": 5.0,  # 1-10のスコア
        "reason": "関連性が高い論文です"
    }


# 後方互換性のための関数エイリアス
def push(progress_callback: Optional[Callable], obj: Dict):
    """進捗通知ヘルパー"""
    if progress_callback:
        progress_callback(json.dumps(obj, ensure_ascii=False))
