"""
CITER2機能のスキーマ
API入出力用のデータ構造定義
"""
import uuid
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class SearchRequest(BaseModel):
    """文献検索リクエスト"""
    text: str = Field(..., description="検索対象のテキスト")
    hits: int = Field(10, ge=1, le=50, description="検索する論文数")
    threshold: int = Field(4, ge=1, le=10, description="引用候補とする最低スコア")
    max_refs: int = Field(3, ge=1, le=10, description="1文あたりの最大引用数")
    model: str = Field("o3", description="使用するモデル")
    author_last_name: Optional[str] = Field(None, description="著者姓（優先検索用）")
    author_first_name: Optional[str] = Field(None, description="著者名（優先検索用）")
    prioritize_author: bool = Field(False, description="著者の論文を優先するか")
    require_high_impact: bool = Field(False, description="高インパクトファクター論文のみ")


class SearchResponse(BaseModel):
    """文献検索レスポンス"""
    job_id: str = Field(..., description="ジョブID")
    status: str = Field(..., description="ジョブステータス")
    message: str = Field("", description="メッセージ")


class JobStatusResponse(BaseModel):
    """ジョブステータスレスポンス"""
    job_id: str
    status: str
    progress: Optional[Dict[str, Any]] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class CitationInfo(BaseModel):
    """引用情報"""
    pmid: str
    title: str
    authors: Optional[str] = None
    journal: Optional[str] = None
    year: Optional[int] = None
    doi: Optional[str] = None
    score: float
    reason: Optional[str] = None


class SearchResultItem(BaseModel):
    """検索結果項目"""
    sentence_index: int
    sentence_text: str
    keyword: Optional[str] = None
    query: Optional[str] = None
    citations: List[CitationInfo]


class SearchResultResponse(BaseModel):
    """検索結果レスポンス"""
    job_id: str
    status: str
    input_text: str
    cited_text: Optional[str] = None
    results: List[SearchResultItem] = []
    ris_url: Optional[str] = None
    report_url: Optional[str] = None
    statistics: Optional[Dict[str, Any]] = None


class UserPreferencesSchema(BaseModel):
    """ユーザー設定スキーマ"""
    default_hits: int = Field(10, ge=1, le=50)
    default_threshold: int = Field(4, ge=1, le=10)
    default_max_refs: int = Field(3, ge=1, le=10)
    default_model: str = Field("o3")
    require_high_impact: bool = Field(False)
    preferred_journals: Optional[List[str]] = None
    excluded_journals: Optional[List[str]] = None
    
    class Config:
        from_attributes = True


class SimpleSearchRequest(BaseModel):
    """シンプル検索リクエスト（Citer2用）"""
    text: str = Field(..., description="検索対象のテキスト")
    hits: int = Field(10, ge=1, le=50, description="検索する論文数")
    model: str = Field("gpt-4.1-mini", description="使用するモデル")


class SimpleSearchResponse(BaseModel):
    """シンプル検索レスポンス（Citer2用）"""
    cited_text: str = Field(..., description="引用が追加されたテキスト")
    ris_content: str = Field("", description="RIS形式の参考文献")
    statistics: Dict[str, Any] = Field(default_factory=dict, description="統計情報")
