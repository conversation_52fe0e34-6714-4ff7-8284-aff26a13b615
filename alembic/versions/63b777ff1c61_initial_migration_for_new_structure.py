"""Initial migration for new structure

Revision ID: 63b777ff1c61
Revises: 
Create Date: 2025-07-18 17:40:04.422562

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '63b777ff1c61'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user',
    sa.Column('role', sa.Integer(), nullable=True),
    sa.Column('gmail_tokens', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('calendar_tokens', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('stripe_customer_id', sa.String(), nullable=True),
    sa.Column('id', fastapi_users_db_sqlalchemy.generics.GUID(), nullable=False),
    sa.Column('email', sa.String(length=320), nullable=False),
    sa.Column('hashed_password', sa.String(length=1024), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_superuser', sa.Boolean(), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('stripe_customer_id')
    )
    op.create_index(op.f('ix_user_email'), 'user', ['email'], unique=True)
    op.create_table('citer2_citations',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('pmid', sa.String(), nullable=False),
    sa.Column('title', sa.Text(), nullable=False),
    sa.Column('authors', sa.Text(), nullable=True),
    sa.Column('journal', sa.String(), nullable=True),
    sa.Column('year', sa.Integer(), nullable=True),
    sa.Column('doi', sa.String(), nullable=True),
    sa.Column('abstract', sa.Text(), nullable=True),
    sa.Column('impact_factor', sa.Float(), nullable=True),
    sa.Column('citation_count', sa.Integer(), nullable=True),
    sa.Column('extra_data', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_citer2_citations_pmid'), 'citer2_citations', ['pmid'], unique=False)
    op.create_table('citer2_search_jobs',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('job_id', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('input_text', sa.Text(), nullable=False),
    sa.Column('parameters', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('result', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_citer2_search_jobs_job_id'), 'citer2_search_jobs', ['job_id'], unique=True)
    op.create_table('citer2_user_preferences',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('default_hits', sa.Integer(), nullable=False),
    sa.Column('default_threshold', sa.Integer(), nullable=False),
    sa.Column('default_max_refs', sa.Integer(), nullable=False),
    sa.Column('default_model', sa.String(), nullable=False),
    sa.Column('require_high_impact', sa.Boolean(), nullable=False),
    sa.Column('preferred_journals', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('excluded_journals', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('hidden_todos',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('task_id', sa.UUID(), nullable=False),
    sa.Column('hidden_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('subscription',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('current_period_end', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('usage_records',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('feature', sa.String(), nullable=False),
    sa.Column('action', sa.String(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('extra_data', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('writer_ocr_jobs',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('job_id', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('file_count', sa.Integer(), nullable=False),
    sa.Column('method', sa.String(), nullable=False),
    sa.Column('result', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_writer_ocr_jobs_job_id'), 'writer_ocr_jobs', ['job_id'], unique=True)
    op.create_table('writer_paper_generations',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('paper_id', sa.String(), nullable=False),
    sa.Column('job_type', sa.String(), nullable=False),
    sa.Column('input_data', postgresql.JSON(astext_type=Text()), nullable=False),
    sa.Column('parameters', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('result', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_writer_paper_generations_paper_id'), 'writer_paper_generations', ['paper_id'], unique=False)
    op.create_table('writer_papers',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('paper_id', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('sections', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('citations', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('extra_data', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('version', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_writer_papers_paper_id'), 'writer_papers', ['paper_id'], unique=True)
    op.create_table('writer_user_preferences',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('default_ocr_method', sa.String(), nullable=False),
    sa.Column('ocr_preprocess', sa.Boolean(), nullable=False),
    sa.Column('default_model', sa.String(), nullable=False),
    sa.Column('auto_citation', sa.Boolean(), nullable=False),
    sa.Column('preferred_citation_style', sa.String(), nullable=False),
    sa.Column('language', sa.String(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('citer2_search_results',
    sa.Column('job_id', sa.UUID(), nullable=False),
    sa.Column('sentence_index', sa.Integer(), nullable=False),
    sa.Column('sentence_text', sa.Text(), nullable=False),
    sa.Column('keyword', sa.String(), nullable=True),
    sa.Column('query', sa.String(), nullable=True),
    sa.Column('pmid', sa.String(), nullable=False),
    sa.Column('score', sa.Float(), nullable=False),
    sa.Column('reason', sa.Text(), nullable=True),
    sa.Column('accepted', sa.Boolean(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['job_id'], ['citer2_search_jobs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('writer_ocr_results',
    sa.Column('job_id', sa.UUID(), nullable=False),
    sa.Column('file_index', sa.Integer(), nullable=False),
    sa.Column('filename', sa.String(), nullable=True),
    sa.Column('method', sa.String(), nullable=False),
    sa.Column('text', sa.Text(), nullable=False),
    sa.Column('confidence', sa.Float(), nullable=True),
    sa.Column('bounding_boxes', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('extra_data', postgresql.JSON(astext_type=Text()), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['job_id'], ['writer_ocr_jobs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('writer_ocr_results')
    op.drop_table('citer2_search_results')
    op.drop_table('writer_user_preferences')
    op.drop_index(op.f('ix_writer_papers_paper_id'), table_name='writer_papers')
    op.drop_table('writer_papers')
    op.drop_index(op.f('ix_writer_paper_generations_paper_id'), table_name='writer_paper_generations')
    op.drop_table('writer_paper_generations')
    op.drop_index(op.f('ix_writer_ocr_jobs_job_id'), table_name='writer_ocr_jobs')
    op.drop_table('writer_ocr_jobs')
    op.drop_table('usage_records')
    op.drop_table('subscription')
    op.drop_table('hidden_todos')
    op.drop_table('citer2_user_preferences')
    op.drop_index(op.f('ix_citer2_search_jobs_job_id'), table_name='citer2_search_jobs')
    op.drop_table('citer2_search_jobs')
    op.drop_index(op.f('ix_citer2_citations_pmid'), table_name='citer2_citations')
    op.drop_table('citer2_citations')
    op.drop_index(op.f('ix_user_email'), table_name='user')
    op.drop_table('user')
    # ### end Alembic commands ###
