# ---------- build stage ----------
FROM python:3.11-slim AS builder
WORKDIR /app
ENV PIP_NO_CACHE_DIR=1

COPY requirements.txt .
# 依存をビルドする場合だけ build-essential を入れる
RUN apt-get update && \
    apt-get install -y --no-install-recommends build-essential && \
    pip install --user -r requirements.txt && \
    apt-get purge -y build-essential && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# ---------- runtime stage ----------
FROM python:3.11-slim
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH=/root/.local/bin:$PATH \
    PORT=8000 \
    LOGLEVEL=INFO

WORKDIR /app

# ----- ここでランタイム依存を一括インストール -----
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        poppler-utils          \
        postgresql-client      \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Python ライブラリをコピー
COPY --from=builder /root/.local /root/.local

# アプリコードをコピー（新しい構造に対応）
COPY app/ /app/app/
COPY static/ /app/static/
COPY alembic/ /app/alembic/
COPY alembic.ini /app/
COPY start.sh /app/
COPY credentials/ /app/credentials/

# start.sh の実行権限設定
RUN chmod +x /app/start.sh

# Google Cloud認証ファイルの権限設定
RUN if [ -f /app/credentials/myaiagents-2025-0ab9fb90c357.json ]; then \
    chmod 644 /app/credentials/myaiagents-2025-0ab9fb90c357.json; \
    fi

EXPOSE 8000
ENTRYPOINT ["/app/start.sh"]